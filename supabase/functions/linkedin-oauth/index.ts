
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_SERVICE_ROLE_KEY') ?? ''
    );

    const { code, userId } = await req.json();
    
    console.log('Processing LinkedIn OAuth for user:', userId);

    // Get the current origin from the request headers
    const origin = req.headers.get('origin') || 'https://mysocialposter.com';
    const redirectUri = `${origin}/thought-leader`;

    console.log('Using redirect URI:', redirectUri);

    // Exchange code for access token
    const tokenResponse = await fetch('https://www.linkedin.com/oauth/v2/accessToken', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
      body: new URLSearchParams({
        grant_type: 'authorization_code',
        code: code,
        redirect_uri: redirectUri,
        client_id: '78o2mnkne785qf',
        client_secret: 'WPL_AP1.TrSFdF9FYdZxKC1d.Q8ij3Q=='
      }).toString()
    });

    if (!tokenResponse.ok) {
      const errorText = await tokenResponse.text();
      console.error('Token exchange failed:', errorText);
      throw new Error(`Failed to exchange code for token: ${errorText}`);
    }

    const tokenData = await tokenResponse.json();
    const accessToken = tokenData.access_token;
    const expiresIn = tokenData.expires_in;

    console.log('Got access token, expires in:', expiresIn, 'seconds');

    // Get user info
    const userInfoResponse = await fetch('https://api.linkedin.com/v2/userinfo', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    if (!userInfoResponse.ok) {
      const errorText = await userInfoResponse.text();
      console.error('Failed to get user info:', errorText);
      throw new Error(`Failed to get user info: ${errorText}`);
    }

    const userInfo = await userInfoResponse.json();
    const linkedInUserId = userInfo.sub;

    console.log('Got LinkedIn user ID:', linkedInUserId, 'User name:', userInfo.name);

    // Calculate expiry date
    const expiresAt = new Date(Date.now() + (expiresIn * 1000)).toISOString();

    // Store token in database - using upsert to handle existing tokens
    const { error: tokenError } = await supabase
      .from('linkedin_tokens')
      .upsert({
        user_id: userId,
        access_token: accessToken,
        linkedin_user_id: linkedInUserId,
        expires_at: expiresAt,
        updated_at: new Date().toISOString()
      }, {
        onConflict: 'user_id'
      });

    if (tokenError) {
      console.error('Error storing token:', tokenError);
      throw new Error(`Failed to store access token: ${tokenError.message}`);
    }

    console.log('Successfully stored LinkedIn token');

    // Update user profile to mark LinkedIn as connected
    const { error: profileError } = await supabase
      .from('profiles')
      .update({ 
        linkedin_connected: true,
        updated_at: new Date().toISOString()
      })
      .eq('id', userId);

    if (profileError) {
      console.error('Error updating profile:', profileError);
      // Don't throw here as token storage was successful
    }

    // Get organizations managed by user
    const orgsResponse = await fetch('https://api.linkedin.com/v2/organizationalEntityAcls?q=roleAssignee&role=ADMINISTRATOR&state=APPROVED', {
      headers: {
        'Authorization': `Bearer ${accessToken}`
      }
    });

    let organizations = [];
    
    if (orgsResponse.ok) {
      const orgsData = await orgsResponse.json();
      console.log('Got organizations data:', orgsData);

      // Get organization details for each org
      const orgPromises = orgsData.elements?.map(async (element: any) => {
        const orgId = element.organizationalTarget;
        
        try {
          const orgDetailResponse = await fetch(`https://api.linkedin.com/v2/organizations/${orgId}`, {
            headers: {
              'Authorization': `Bearer ${accessToken}`
            }
          });

          if (orgDetailResponse.ok) {
            const orgDetail = await orgDetailResponse.json();
            return {
              user_id: userId,
              organization_id: orgId,
              organization_name: orgDetail.localizedName || `Organization ${orgId}`,
              organization_type: orgDetail.organizationType,
              updated_at: new Date().toISOString()
            };
          }
        } catch (error) {
          console.error('Error fetching org details for', orgId, error);
        }
        
        return null;
      }) || [];

      const orgDetails = (await Promise.all(orgPromises)).filter(Boolean);
      
      if (orgDetails.length > 0) {
        // Clear existing organizations for this user
        await supabase
          .from('linkedin_organizations')
          .delete()
          .eq('user_id', userId);

        // Insert new organizations
        const { error: orgsError } = await supabase
          .from('linkedin_organizations')
          .insert(orgDetails);

        if (orgsError) {
          console.error('Error storing organizations:', orgsError);
        } else {
          console.log('Successfully stored', orgDetails.length, 'organizations');
          organizations = orgDetails.map(org => ({
            id: org.id,
            name: org.organization_name,
            type: org.organization_type,
            organizationId: org.organization_id
          }));
        }
      }
    } else {
      console.log('Failed to fetch organizations, but continuing...');
    }

    return new Response(
      JSON.stringify({
        success: true,
        id: linkedInUserId,
        name: userInfo.name || 'LinkedIn User',
        organizations
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('LinkedIn OAuth error:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message || 'Failed to process LinkedIn OAuth' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
