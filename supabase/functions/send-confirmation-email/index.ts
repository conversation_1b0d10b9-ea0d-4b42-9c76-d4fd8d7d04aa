
import { serve } from "https://deno.land/std@0.190.0/http/server.ts";
import { Resend } from "npm:resend@2.0.0";

const resend = new Resend(Deno.env.get("RESEND_API_KEY"));

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers":
    "authorization, x-client-info, apikey, content-type",
};

interface User {
  id: string;
  email: string;
  name: string;
}

const handler = async (req: Request): Promise<Response> => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const { user }: { user: User } = await req.json();

    if (!user || !user.email || !user.name) {
      throw new Error("Missing required user information");
    }

    console.log("Sending welcome email to:", user.email);
    
    // Get verified domain - use mysocialposter.com as the domain
    const verifiedDomain = Deno.env.get("VERIFIED_DOMAIN") || "mysocialposter.com";
    
    // <NAME_EMAIL> as the from address
    const fromEmail = `Social Poster <support@${verifiedDomain}>`;
    
    console.log(`Using from email: ${fromEmail}`);
    console.log(`Sending to: ${user.email}`);
    
    const emailResponse = await resend.emails.send({
      from: fromEmail,
      to: [user.email],
      subject: "Welcome to Social Poster!",
      html: `
        <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto;">
          <div style="text-align: center; margin-bottom: 20px;">
            <h2 style="color: #0e76a8;">Social Poster</h2>
          </div>
          <h1 style="color: #0e76a8; text-align: center;">Welcome to Social Poster!</h1>
          <p>Hello ${user.name},</p>
          <p>Thank you for creating an account with Social Poster. Your account has been successfully created with the following details:</p>
          <div style="background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin: 20px 0;">
            <p><strong>Name:</strong> ${user.name}</p>
            <p><strong>Email:</strong> ${user.email}</p>
          </div>
          <p>With Social Poster, you can:</p>
          <ul>
            <li>Schedule posts to multiple social media platforms</li>
            <li>Connect your LinkedIn account for seamless posting</li>
            <li>Manage your content preferences</li>
            <li>Track your posting activity</li>
          </ul>
          <p>If you have any questions or need assistance, please don't hesitate to contact our support team at <a href="mailto:<EMAIL>"><EMAIL></a>.</p>
          <p>Best regards,</p>
          <p>The Social Poster Team</p>
          <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; color: #777; font-size: 12px;">
            <p>© ${new Date().getFullYear()} Social Poster. All rights reserved.</p>
          </div>
        </div>
      `,
    });

    if (emailResponse.error) {
      console.error("Error from Resend API:", emailResponse.error);
      return new Response(
        JSON.stringify({ 
          error: emailResponse.error.message,
          details: emailResponse.error,
          suggestions: [
            "Ensure your domain is verified at https://resend.com/domains",
            "Check that your from address uses your verified domain",
            "Make sure you've upgraded from the free tier if you're sending to multiple recipients"
          ]
        }),
        {
          status: 500,
          headers: { "Content-Type": "application/json", ...corsHeaders },
        }
      );
    }

    console.log("Confirmation email sent successfully:", emailResponse);

    return new Response(JSON.stringify({
      success: true,
      message: `Email sent to ${user.email}`,
      data: emailResponse
    }), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
        ...corsHeaders,
      },
    });
  } catch (error: any) {
    console.error("Error in send-confirmation-email function:", error);
    return new Response(
      JSON.stringify({ error: error.message }),
      {
        status: 500,
        headers: { "Content-Type": "application/json", ...corsHeaders },
      }
    );
  }
};

serve(handler);
