
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    // Get LinkedIn Client ID from environment variables
    const linkedinClientId = Deno.env.get('LINKEDIN_CLIENT_ID');
    
    if (!linkedinClientId || linkedinClientId.trim() === '') {
      console.error('LinkedIn Client ID is not configured in Supabase secrets');
      return new Response(
        JSON.stringify({ 
          success: false,
          error: 'LinkedIn integration is not configured correctly' 
        }),
        {
          status: 500,
          headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        }
      );
    }

    console.log('Providing LinkedIn configuration');

    return new Response(
      JSON.stringify({
        success: true,
        clientId: linkedinClientId
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error getting LinkedIn configuration:', error);
    return new Response(
      JSON.stringify({ 
        success: false,
        error: error.message || 'Failed to get LinkedIn configuration' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
