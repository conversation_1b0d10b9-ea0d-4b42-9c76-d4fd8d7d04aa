
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import Strip<PERSON> from "https://esm.sh/stripe@12.18.0?target=deno";
import { createClient } from "https://esm.sh/@supabase/supabase-js@2.38.4";

const corsHeaders = {
  "Access-Control-Allow-Origin": "*",
  "Access-Control-Allow-Headers": "authorization, x-client-info, apikey, content-type",
};

const STRIPE_SECRET_KEY = Deno.env.get("STRIPE_SECRET_KEY") || "";
const SUPABASE_URL = Deno.env.get("SUPABASE_URL") || "https://jgupgbgfxvhhzitftjzi.supabase.co";
const SUPABASE_SERVICE_ROLE_KEY = Deno.env.get("SUPABASE_SERVICE_ROLE_KEY") || "";

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === "OPTIONS") {
    return new Response(null, {
      headers: corsHeaders,
      status: 204,
    });
  }

  try {
    const authHeader = req.headers.get("Authorization");
    if (!authHeader) {
      return new Response(JSON.stringify({ error: "No authorization header" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 401,
      });
    }
    
    const token = authHeader.replace("Bearer ", "");
    const supabase = createClient(SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY, {
      auth: { persistSession: false }
    });
    
    // Validate the user token
    const { data: { user }, error: userError } = await supabase.auth.getUser(token);
    if (userError || !user) {
      return new Response(JSON.stringify({ error: "Invalid user token" }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 401,
      });
    }
    
    // Get the user's Stripe customer ID
    // Use a more explicit approach to handle possible type issues
    const { data: profileData } = await supabase
      .from("profiles")
      .select("*")  // Using * instead of specific fields to avoid type issues
      .eq("id", user.id)
      .maybeSingle();
    
    const profile = profileData as any; // Cast to any to safely access fields
    const stripeCustomerId = profile?.stripe_customer_id;
    
    if (!stripeCustomerId) {
      // User has no Stripe customer ID, so no subscriptions
      return new Response(JSON.stringify({ 
        subscriptions: [], 
        customer_id: null 
      }), {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 200,
      });
    }
    
    const stripe = new Stripe(STRIPE_SECRET_KEY, {
      apiVersion: "2023-10-16",
    });
    
    // Get all active subscriptions for the customer
    const subscriptions = await stripe.subscriptions.list({
      customer: stripeCustomerId,
      status: "active",
      expand: ["data.default_payment_method"],
    });
    
    // Format the subscription data
    const formattedSubscriptions = await Promise.all(subscriptions.data.map(async (sub) => {
      const item = sub.items.data[0];
      const productId = sub.metadata.product_id;
      
      // Get product preferences if they exist
      const { data: preferences } = await supabase
        .from("ai_agent_preferences")
        .select("preferences")
        .eq("user_id", user.id)
        .eq("agent_id", productId)
        .maybeSingle();
      
      return {
        id: sub.id,
        status: sub.status,
        current_period_end: sub.current_period_end,
        trial_end: sub.trial_end,
        cancel_at_period_end: sub.cancel_at_period_end,
        product_id: productId,
        amount: item.price.unit_amount,
        currency: item.price.currency,
        preferences: preferences?.preferences || {},
      };
    }));
    
    // Update subscriptions in Supabase for persistence
    for (const sub of formattedSubscriptions) {
      await supabase
        .from("subscriptions")
        .upsert({
          user_id: user.id,
          stripe_subscription_id: sub.id,
          plan: sub.product_id,
          status: sub.status,
          price: sub.amount / 100, // Convert from cents to dollars
          started_at: new Date().toISOString(),
          expires_at: new Date(sub.current_period_end * 1000).toISOString(),
          updated_at: new Date().toISOString(),
        }, {
          onConflict: 'stripe_subscription_id',
        });
    }
    
    return new Response(JSON.stringify({ 
      subscriptions: formattedSubscriptions,
      customer_id: stripeCustomerId 
    }), {
      headers: { ...corsHeaders, "Content-Type": "application/json" },
      status: 200,
    });
  } catch (error) {
    console.error("Error checking subscriptions:", error);
    return new Response(
      JSON.stringify({ error: error.message || "Unknown error occurred" }),
      {
        headers: { ...corsHeaders, "Content-Type": "application/json" },
        status: 500,
      }
    );
  }
});
