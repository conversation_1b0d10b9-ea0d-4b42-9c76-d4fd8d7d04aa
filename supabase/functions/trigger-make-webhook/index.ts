
import "https://deno.land/x/xhr@0.1.0/mod.ts";
import { serve } from "https://deno.land/std@0.168.0/http/server.ts";
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2.49.4';

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
};

serve(async (req) => {
  if (req.method === 'OPTIONS') {
    return new Response(null, { headers: corsHeaders });
  }

  try {
    const supabase = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? ''
    );

    const { webhookUrl, userId, sentiment, prompt, linkedInConnected, scheduledId } = await req.json();
    
    console.log('Triggering Make.com webhook for user:', userId);
    console.log('Webhook URL:', webhookUrl);
    console.log('Scheduled ID:', scheduledId);

    // Get user session to retrieve LinkedIn token if available
    let linkedInAccessToken = null;
    
    if (linkedInConnected) {
      // Note: In a real implementation, you would store and retrieve the LinkedIn token
      // For now, we'll indicate that LinkedIn is connected
      linkedInAccessToken = "linkedin_connected";
    }

    // Prepare webhook payload
    const webhookPayload = {
      userId,
      sentiment,
      prompt,
      linkedInConnected: !!linkedInAccessToken,
      linkedInAccessToken,
      timestamp: new Date().toISOString(),
      scheduledId,
      topic: "Scheduled LinkedIn Post", // Default topic for scheduled posts
    };

    console.log('Webhook payload:', webhookPayload);

    // Call the Make.com webhook
    const webhookResponse = await fetch(webhookUrl, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(webhookPayload),
    });

    if (!webhookResponse.ok) {
      throw new Error(`Webhook call failed: ${webhookResponse.status}`);
    }

    console.log('Webhook triggered successfully');

    // Log the webhook trigger in the database
    const { error: logError } = await supabase
      .from('posts')
      .insert({
        user_id: userId,
        content: `Scheduled post triggered: ${prompt}`,
        platform: 'linkedin',
        status: 'scheduled',
        scheduled_for: new Date().toISOString(),
      });

    if (logError) {
      console.error('Error logging webhook trigger:', logError);
    }

    return new Response(
      JSON.stringify({ 
        success: true, 
        message: 'Webhook triggered successfully',
        scheduledId 
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );

  } catch (error) {
    console.error('Error triggering webhook:', error);
    return new Response(
      JSON.stringify({ 
        error: error.message || 'Failed to trigger webhook' 
      }),
      {
        status: 500,
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
      }
    );
  }
});
