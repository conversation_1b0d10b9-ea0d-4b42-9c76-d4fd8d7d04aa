
-- Create table for AI agent user preferences
CREATE TABLE IF NOT EXISTS public.ai_agent_preferences (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  agent_id TEXT NOT NULL,
  preferences JSONB NOT NULL DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Add a unique constraint to ensure one preference set per user per agent
ALTER TABLE public.ai_agent_preferences ADD CONSTRAINT unique_user_agent_preferences UNIQUE (user_id, agent_id);

-- Enable RLS on the table
ALTER TABLE public.ai_agent_preferences ENABLE ROW LEVEL SECURITY;

-- RLS policy for users to view their own preferences
CREATE POLICY "Users can view their own preferences" 
  ON public.ai_agent_preferences 
  FOR SELECT 
  USING (auth.uid() = user_id);

-- RLS policy for users to insert their own preferences
CREATE POLICY "Users can insert their own preferences" 
  ON public.ai_agent_preferences 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

-- RLS policy for users to update their own preferences
CREATE POLICY "Users can update their own preferences" 
  ON public.ai_agent_preferences 
  FOR UPDATE 
  USING (auth.uid() = user_id);

-- Add stripe_customer_id column to profiles if it doesn't exist
ALTER TABLE public.profiles ADD COLUMN IF NOT EXISTS stripe_customer_id TEXT;
