
-- Create a table to store scheduled posts with webhook triggers
CREATE TABLE IF NOT EXISTS public.scheduled_webhook_posts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
  agent_id TEXT NOT NULL DEFAULT 'linkedin-thought-leader',
  webhook_url TEXT NOT NULL,
  scheduled_time TIME NOT NULL,
  days_of_week TEXT[] NOT NULL,
  sentiment TEXT NOT NULL,
  prompt TEXT NOT NULL,
  is_active BOOLEAN NOT NULL DEFAULT true,
  last_triggered_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
  updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now()
);

-- Enable RLS
ALTER TABLE public.scheduled_webhook_posts ENABLE ROW LEVEL SECURITY;

-- RLS policies
CREATE POLICY "Users can view their own scheduled posts" 
  ON public.scheduled_webhook_posts 
  FOR SELECT 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own scheduled posts" 
  ON public.scheduled_webhook_posts 
  FOR INSERT 
  WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own scheduled posts" 
  ON public.scheduled_webhook_posts 
  FOR UPDATE 
  USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own scheduled posts" 
  ON public.scheduled_webhook_posts 
  FOR DELETE 
  USING (auth.uid() = user_id);

-- Enable pg_cron extension if not already enabled
CREATE EXTENSION IF NOT EXISTS pg_cron;

-- Create a function to trigger scheduled webhooks
CREATE OR REPLACE FUNCTION public.trigger_scheduled_webhooks()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
DECLARE
  webhook_record RECORD;
  current_day_name TEXT;
  current_time_val TIME;
BEGIN
  -- Get current day and time (using different variable names to avoid conflicts)
  current_day_name := LOWER(TRIM(TO_CHAR(NOW(), 'Day')));
  current_time_val := NOW()::TIME;
  
  -- Find all active scheduled posts that should be triggered now
  FOR webhook_record IN
    SELECT sp.*, p.linkedin_connected
    FROM public.scheduled_webhook_posts sp
    JOIN public.profiles p ON p.id = sp.user_id
    WHERE sp.is_active = true
      AND current_day_name = ANY(sp.days_of_week)
      AND sp.scheduled_time <= current_time_val
      AND sp.scheduled_time > (current_time_val - INTERVAL '1 minute')
      AND (sp.last_triggered_at IS NULL OR sp.last_triggered_at < CURRENT_DATE)
  LOOP
    -- Call the webhook via edge function
    PERFORM net.http_post(
      url := 'https://jgupgbgfxvhhzitftjzi.supabase.co/functions/v1/trigger-make-webhook',
      headers := '{"Content-Type": "application/json", "Authorization": "Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpndXBnYmdmeHZoaHppdGZ0anppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTM5NTQsImV4cCI6MjA2MjYyOTk1NH0.T6EOZ49GXtoAclMM2nUbIVYrgFKmtE2sjcwEMev7G8s"}'::jsonb,
      body := json_build_object(
        'webhookUrl', webhook_record.webhook_url,
        'userId', webhook_record.user_id,
        'sentiment', webhook_record.sentiment,
        'prompt', webhook_record.prompt,
        'linkedInConnected', webhook_record.linkedin_connected,
        'scheduledId', webhook_record.id
      )::jsonb
    );
    
    -- Update last_triggered_at
    UPDATE public.scheduled_webhook_posts 
    SET last_triggered_at = NOW(), updated_at = NOW()
    WHERE id = webhook_record.id;
  END LOOP;
END;
$$;

-- Schedule the cron job to run every minute
SELECT cron.schedule(
  'trigger-scheduled-webhooks',
  '* * * * *', -- every minute
  'SELECT public.trigger_scheduled_webhooks();'
);
