
# A string used to distinguish different Supabase projects on the same host. Defaults to the
# working directory name when running `supabase init`.
project_id = "jgupgbgfxvhhzitftjzi"

[auth]
# The base URL of your website. Used as an allow-list for redirects and for constructing URLs used
# in emails.
site_url = "https://mysocialposter.com"
# A list of *exact* URLs that auth providers are permitted to redirect to post authentication.
additional_redirect_urls = [
  "https://localhost:3000", 
  "http://localhost:3000/login", 
  "http://localhost:3000/dashboard",
  "http://localhost:3000/settings",
  "https://mysocialposter.com",
  "https://mysocialposter.com/login",
  "https://mysocialposter.com/dashboard",
  "https://mysocialposter.com/settings"
]
# How long tokens are valid for, in seconds. Defaults to 3600 (1 hour), maximum 604,800 (1 week).
jwt_expiry = 3600
# If disabled, the refresh token will never expire.
enable_refresh_token_rotation = true
# Allows refresh tokens to be reused after expiry, up to the specified interval in seconds.
# Requires enable_refresh_token_rotation = true.
refresh_token_reuse_interval = 10
# Allow/disallow new user signups to your project.
enable_signup = true

[auth.email]
# Allow/disallow new user signups via email to your project.
enable_signup = true
# If enabled, a user will be required to confirm any email change on both the old, and new email
# addresses. If disabled, only the new email is required to confirm.
double_confirm_changes = true
# If enabled, users need to confirm their email address before signing in.
enable_confirmations = false

[auth.google]
# Allow/disallow new user signups via Google to your project.
enabled = true
client_id = "env(GOOGLE_CLIENT_ID)"
secret = "env(GOOGLE_CLIENT_SECRET)"

[auth.linkedin_oidc]
enabled = true
client_id = "78o2mnkne785qf"
secret = "WPL_AP1.TrSFdF9FYdZxKC1d.Q8ij3Q=="
