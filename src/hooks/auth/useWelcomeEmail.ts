
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';

export function useWelcomeEmail() {
  const sendWelcomeEmail = useCallback(async (userId: string, email: string, name: string) => {
    try {
      console.log("Sending welcome email to:", email);
      const { error } = await supabase.functions.invoke('send-confirmation-email', {
        body: { 
          user: { 
            id: userId,
            email,
            name
          } 
        }
      });
      
      if (error) {
        console.error("Error sending welcome email:", error);
      } else {
        console.log("Welcome email sent successfully to:", email);
      }
    } catch (error) {
      console.error("Error invoking send-confirmation-email function:", error);
    }
  }, []);

  return { sendWelcomeEmail };
}
