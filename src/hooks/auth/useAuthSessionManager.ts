
import { useState, useCallback } from 'react';
import { Session } from '@supabase/supabase-js';
import { supabase } from '@/integrations/supabase/client';

export function useAuthSessionManager() {
  const [session, setSession] = useState<Session | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  const refreshSession = useCallback(async () => {
    try {
      const { data, error } = await supabase.auth.refreshSession();
      if (error) throw error;
      
      setSession(data.session);
      return data.session;
    } catch (error: any) {
      console.error("Session refresh error:", error);
      return null;
    }
  }, []);

  return {
    session,
    setSession,
    isLoading,
    setIsLoading,
    refreshSession
  };
}
