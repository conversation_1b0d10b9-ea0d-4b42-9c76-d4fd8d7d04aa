
import { useState, useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { UserWithSubscription } from '@/types/auth';

export function useUserProfileManager() {
  const [user, setUser] = useState<UserWithSubscription | null>(null);
  const [linkedin_connected, setLinkedInConnected] = useState(false);

  const fetchUserProfile = useCallback(async (userId: string) => {
    try {
      const { data: profile, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) {
        console.error("Error fetching profile:", error);
        return;
      }

      if (profile) {
        // Mock subscription data
        const mockSubscription = {
          plan: 'free',
          status: 'active'
        };

        // Get the user's auth metadata to check for LinkedIn connection
        const { data: { user: authUser } } = await supabase.auth.getUser();
        
        // Check if LinkedIn is connected via providers in auth metadata
        const providers = authUser?.app_metadata?.providers || [];
        const linkedInConnected = providers.includes('linkedin_oidc');
        
        // Update the profile with the most accurate LinkedIn connection status
        const isLinkedInConnected = linkedInConnected || profile.linkedin_connected || false;
        
        // If the database doesn't match the auth metadata, update the database
        if (profile.linkedin_connected !== isLinkedInConnected) {
          const { error: updateError } = await supabase
            .from('profiles')
            .update({ linkedin_connected: isLinkedInConnected })
            .eq('id', userId);
            
          if (updateError) {
            console.error("Error updating LinkedIn connection status:", updateError);
          }
        }

        setUser({
          ...profile,
          linkedin_connected: isLinkedInConnected,
          subscription: mockSubscription
        });
        setLinkedInConnected(isLinkedInConnected);
      }
    } catch (error) {
      console.error("Error fetching user profile:", error);
    }
  }, []);

  const updateLinkedInConnected = useCallback(async (value: boolean) => {
    if (!user) return;

    try {
      const { error } = await supabase
        .from('profiles')
        .update({ linkedin_connected: value })
        .eq('id', user.id);

      if (error) {
        console.error("Error updating LinkedIn connection status:", error);
        return;
      }
      
      setLinkedInConnected(value);
      setUser(prev => prev ? { ...prev, linkedin_connected: value } : null);
    } catch (error) {
      console.error("Error updating LinkedIn connection status:", error);
    }
  }, [user]);

  return {
    user,
    setUser,
    linkedin_connected,
    setLinkedInConnected,
    fetchUserProfile,
    updateLinkedInConnected
  };
}
