
import { useCallback } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { toast } from "@/hooks/use-toast";
import { SignUpResult } from '@/types/auth';

export function useAuthMethods(setIsLoading: (value: boolean) => void) {
  const signIn = useCallback(async (email: string, password: string) => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) throw error;
      return { user: data.user, session: data.session };
    } catch (error: any) {
      console.error("Sign in error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  const signUp = useCallback(async (email: string, password: string, name: string): Promise<SignUpResult> => {
    setIsLoading(true);
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name,
          },
        },
      });

      if (error) throw error;
      return { user: data.user, session: data.session };
    } catch (error: any) {
      console.error("Sign up error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  const signInWithGoogle = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) throw error;
      // OAuth redirects happen automatically, so we don't return data
    } catch (error: any) {
      console.error("Google sign in error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  const signInWithApple = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'apple',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) throw error;
      // OAuth redirects happen automatically, so we don't return data
    } catch (error: any) {
      console.error("Apple sign in error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  const signInWithLinkedIn = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signInWithOAuth({
        provider: 'linkedin_oidc',
        options: {
          redirectTo: `${window.location.origin}/dashboard`
        }
      });

      if (error) throw error;
      // OAuth redirects happen automatically, so we don't return data
    } catch (error: any) {
      console.error("LinkedIn sign in error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  const signOut = useCallback(async () => {
    setIsLoading(true);
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
    } catch (error: any) {
      console.error("Sign out error:", error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [setIsLoading]);

  return {
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithApple,
    signInWithLinkedIn
  };
}
