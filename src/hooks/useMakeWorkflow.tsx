
import { useState } from "react";
import { useToast } from "@/hooks/use-toast";

export interface MakeWorkflowParams {
  topic: string;
  sentiment: string;
  prompt: string;
  userId: string;
  linkedInEntityType?: 'user' | 'organization';
  linkedInEntityId?: string;
  linkedInEntityName?: string;
}

export function useMakeWorkflow() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const triggerWorkflow = async (webhookUrl: string, params: MakeWorkflowParams) => {
    setIsLoading(true);
    try {
      console.log("Triggering Make.com workflow with params:", params);

      const response = await fetch(webhookUrl, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          ...params,
          timestamp: new Date().toISOString(),
        }),
      });

      if (!response.ok) {
        throw new Error(`Workflow trigger failed: ${response.status}`);
      }

      console.log("Make.com workflow triggered successfully");

      toast({
        title: "Content Generation Started",
        description: "Your LinkedIn content is being generated. You'll be notified when it's ready.",
      });

    } catch (error: any) {
      console.error("Error triggering workflow:", error);
      toast({
        title: "Error",
        description: error.message || "Failed to trigger content generation. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return {
    triggerWorkflow,
    isLoading,
  };
}
