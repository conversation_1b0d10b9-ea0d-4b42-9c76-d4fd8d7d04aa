
import { useState, useEffect, createContext, useContext, ReactNode } from 'react';
import { useAuth } from '@/context/AuthContext';
import { supabase } from '@/integrations/supabase/client';
import { useToast } from '@/components/ui/use-toast';

interface Subscription {
  id: string;
  status: string;
  product_id: string;
  current_period_end: number;
  trial_end: number | null;
  cancel_at_period_end: boolean;
  preferences?: any;
}

interface SubscriptionContextType {
  subscriptions: Subscription[];
  isLoading: boolean;
  hasActiveSubscription: (productId?: string) => boolean;
  refreshSubscriptions: () => Promise<void>;
  customerPortalUrl: string | null;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(undefined);

export const SubscriptionProvider = ({ children }: { children: ReactNode }) => {
  const { user, isAuthenticated } = useAuth();
  const [subscriptions, setSubscriptions] = useState<Subscription[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [customerPortalUrl, setCustomerPortalUrl] = useState<string | null>(null);
  const [lastRefresh, setLastRefresh] = useState<number>(0);
  const { toast } = useToast();

  const refreshSubscriptions = async () => {
    if (!isAuthenticated || !user) {
      setSubscriptions([]);
      setIsLoading(false);
      return;
    }

    // Implement rate limiting - don't refresh more than once every 30 seconds
    const now = Date.now();
    if (now - lastRefresh < 30000) {
      console.log('Skipping subscription refresh - rate limited');
      return;
    }

    try {
      setIsLoading(true);
      setLastRefresh(now);
      
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        setSubscriptions([]);
        return;
      }

      console.log('Checking subscription status...');
      const { data, error } = await supabase.functions.invoke('check-subscription', {
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) {
        console.error('Subscription check error:', error);
        // Don't show toast for rate limit errors
        if (!error.message?.includes('rate limit')) {
          toast({
            title: 'Error',
            description: 'Failed to load subscription information.',
            variant: 'destructive',
          });
        }
        return;
      }

      console.log('Subscription data received:', data);
      setSubscriptions(data.subscriptions || []);
      
    } catch (error) {
      console.error('Error refreshing subscriptions:', error);
      // Only show toast for non-rate-limit errors
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';
      if (!errorMessage.includes('rate limit')) {
        toast({
          title: 'Error',
          description: 'Failed to load subscription information.',
          variant: 'destructive',
        });
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Only refresh on mount and when authentication status changes
  useEffect(() => {
    if (isAuthenticated && user) {
      refreshSubscriptions();
    } else {
      setSubscriptions([]);
      setIsLoading(false);
    }
  }, [isAuthenticated, user?.id]); // Only depend on user ID, not the full user object

  const hasActiveSubscription = (productId?: string) => {
    if (!productId) {
      return subscriptions.some(sub => 
        sub.status === 'active' || sub.status === 'trialing'
      );
    }
    
    return subscriptions.some(sub => 
      (sub.status === 'active' || sub.status === 'trialing') && 
      sub.product_id === productId
    );
  };

  return (
    <SubscriptionContext.Provider 
      value={{ 
        subscriptions, 
        isLoading, 
        hasActiveSubscription, 
        refreshSubscriptions,
        customerPortalUrl
      }}
    >
      {children}
    </SubscriptionContext.Provider>
  );
};

export const useSubscription = () => {
  const context = useContext(SubscriptionContext);
  if (!context) {
    throw new Error('useSubscription must be used within a SubscriptionProvider');
  }
  return context;
};
