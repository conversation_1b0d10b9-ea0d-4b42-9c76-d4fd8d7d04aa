
import { useState, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

export type DayOfWeek = "monday" | "tuesday" | "wednesday" | "thursday" | "friday" | "saturday" | "sunday";

export interface ScheduledWebhookPost {
  id: string;
  user_id: string;
  agent_id: string;
  webhook_url: string;
  scheduled_time: string;
  days_of_week: DayOfWeek[];
  sentiment: string;
  prompt: string;
  is_active: boolean;
  last_triggered_at?: string;
  created_at: string;
  updated_at: string;
}

export function useScheduledWebhooks() {
  const [scheduledPosts, setScheduledPosts] = useState<ScheduledWebhookPost[]>([]);
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadScheduledPosts();
    }
  }, [isAuthenticated, user]);

  const loadScheduledPosts = async () => {
    if (!user) return;
    
    try {
      setLoading(true);
      
      // Use direct table access with explicit typing
      const { data, error } = await supabase
        .from('scheduled_webhook_posts' as any)
        .select('*')
        .eq('user_id', user.id)
        .order('created_at', { ascending: false });
      
      if (error) {
        console.error("Supabase error:", error);
        throw error;
      }
      
      // Convert to unknown first, then to our type
      const typedData = (data as unknown) as ScheduledWebhookPost[];
      setScheduledPosts(typedData || []);
    } catch (error) {
      console.error("Error loading scheduled posts:", error);
      toast({
        title: "Error loading scheduled posts",
        description: "Could not load your scheduled posts. Please try again.",
        variant: "destructive",
      });
      // Set empty array on error
      setScheduledPosts([]);
    } finally {
      setLoading(false);
    }
  };

  const createScheduledPost = async (postData: Omit<ScheduledWebhookPost, 'id' | 'user_id' | 'created_at' | 'updated_at' | 'last_triggered_at'>) => {
    if (!user) {
      toast({
        title: "Authentication required",
        description: "You must be logged in to schedule posts.",
        variant: "destructive",
      });
      return null;
    }

    try {
      setLoading(true);
      
      // Use direct table access with type assertion
      const { data, error } = await supabase
        .from('scheduled_webhook_posts' as any)
        .insert({
          user_id: user.id,
          ...postData,
        })
        .select()
        .single();
      
      if (error) throw error;
      
      toast({
        title: "Post scheduled",
        description: "Your post has been scheduled successfully.",
      });
      
      await loadScheduledPosts();
      return (data as unknown) as ScheduledWebhookPost;
    } catch (error: any) {
      console.error("Error creating scheduled post:", error);
      toast({
        title: "Error scheduling post",
        description: error.message || "Could not schedule your post. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setLoading(false);
    }
  };

  const updateScheduledPost = async (id: string, updates: Partial<ScheduledWebhookPost>) => {
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('scheduled_webhook_posts' as any)
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', id);
      
      if (error) throw error;
      
      toast({
        title: "Post updated",
        description: "Your scheduled post has been updated.",
      });
      
      await loadScheduledPosts();
    } catch (error: any) {
      console.error("Error updating scheduled post:", error);
      toast({
        title: "Error updating post",
        description: error.message || "Could not update your post. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const deleteScheduledPost = async (id: string) => {
    try {
      setLoading(true);
      
      const { error } = await supabase
        .from('scheduled_webhook_posts' as any)
        .delete()
        .eq('id', id);
      
      if (error) throw error;
      
      toast({
        title: "Post deleted",
        description: "Your scheduled post has been deleted.",
      });
      
      await loadScheduledPosts();
    } catch (error: any) {
      console.error("Error deleting scheduled post:", error);
      toast({
        title: "Error deleting post",
        description: error.message || "Could not delete your post. Please try again.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return {
    scheduledPosts,
    loading,
    createScheduledPost,
    updateScheduledPost,
    deleteScheduledPost,
    refreshScheduledPosts: loadScheduledPosts,
  };
}
