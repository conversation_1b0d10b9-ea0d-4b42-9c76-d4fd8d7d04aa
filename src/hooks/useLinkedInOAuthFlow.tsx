
import { useState, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

export interface LinkedInOrganization {
  id: string;
  name: string;
  vanityName?: string;
}

export interface LinkedInProfile {
  linkedinUserId: string;
  name: string;
  email?: string;
  picture?: string;
  organizations: LinkedInOrganization[];
}

export function useLinkedInOAuthFlow() {
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();
  const { user, refreshSession } = useAuth();

  // Generate a secure random state string for CSRF protection
  const generateState = useCallback(() => {
    const array = new Uint8Array(16);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  }, []);

  // Get LinkedIn configuration from Supabase
  const getLinkedInConfig = useCallback(async () => {
    try {
      const { data, error } = await supabase.functions.invoke('get-linkedin-config');

      if (error) {
        console.error('Error getting LinkedIn config:', error);
        throw new Error('Failed to get LinkedIn configuration');
      }

      if (!data.success) {
        console.error('LinkedIn config failed:', data.error);
        throw new Error(data.error || 'LinkedIn configuration is not available');
      }

      return data.clientId;
    } catch (error: any) {
      console.error('LinkedIn config error:', error);
      throw new Error(error.message || 'Failed to get LinkedIn configuration');
    }
  }, []);

  // Start LinkedIn OAuth flow
  const startLinkedInAuth = useCallback(async () => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to connect LinkedIn",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsLoading(true);

      // Get LinkedIn Client ID from Supabase
      const linkedinClientId = await getLinkedInConfig();

      // Generate and store state for CSRF protection
      const state = generateState();
      sessionStorage.setItem('linkedin_oauth_state', state);
      
      // Get current origin and construct redirect URI
      const redirectUri = `${window.location.origin}/settings`;
      
      // LinkedIn OAuth parameters
      const params = new URLSearchParams({
        response_type: 'code',
        client_id: linkedinClientId,
        redirect_uri: redirectUri,
        state: state,
        scope: 'openid profile email w_member_social rw_organization_admin'
      });

      // Redirect to LinkedIn authorization URL
      const authUrl = `https://www.linkedin.com/oauth/v2/authorization?${params.toString()}`;
      console.log('Redirecting to LinkedIn OAuth URL:', authUrl);
      window.location.href = authUrl;
    } catch (error: any) {
      console.error('Error starting LinkedIn OAuth:', error);
      toast({
        title: "Configuration Error",
        description: error.message || "LinkedIn integration is not configured correctly. Please contact support.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, generateState, getLinkedInConfig, toast]);

  // Handle LinkedIn OAuth callback
  const handleLinkedInCallback = useCallback(async (code: string, state: string): Promise<LinkedInProfile | null> => {
    if (!user) {
      console.error("No authenticated user found");
      toast({
        title: "Authentication Error",
        description: "You must be logged in to connect LinkedIn",
        variant: "destructive",
      });
      return null;
    }

    setIsLoading(true);
    try {
      // Verify state parameter for CSRF protection
      const storedState = sessionStorage.getItem('linkedin_oauth_state');
      if (!storedState || storedState !== state) {
        throw new Error('Invalid state parameter - possible CSRF attack');
      }

      // Clean up stored state
      sessionStorage.removeItem('linkedin_oauth_state');

      console.log('Processing LinkedIn OAuth callback...');

      // Get current user's JWT token
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        throw new Error('No valid session found');
      }

      // Call Supabase Edge Function
      const { data, error } = await supabase.functions.invoke('linkedin-oauth-callback', {
        body: { 
          code, 
          userId: user.id 
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) {
        console.error('Edge function error:', error);
        throw new Error(error.message || 'Failed to process LinkedIn OAuth');
      }

      if (!data.success) {
        console.error('LinkedIn OAuth failed:', data.error);
        throw new Error(data.error || 'LinkedIn OAuth failed');
      }

      console.log('LinkedIn OAuth successful:', data);

      // Refresh user session to update linkedin_connected status
      await refreshSession();

      toast({
        title: "LinkedIn Connected",
        description: `Successfully connected to LinkedIn as ${data.name}`,
      });

      return {
        linkedinUserId: data.linkedinUserId,
        name: data.name,
        email: data.email,
        picture: data.picture,
        organizations: data.organizations || []
      };

    } catch (error: any) {
      console.error('LinkedIn OAuth callback error:', error);
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect to LinkedIn. Please try again.",
        variant: "destructive",
      });
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [user, toast, refreshSession]);

  return {
    isLoading,
    startLinkedInAuth,
    handleLinkedInCallback,
  };
}
