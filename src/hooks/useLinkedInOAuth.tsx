import { useState, useCallback } from "react";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

export interface LinkedInOrganization {
  id: string;
  name: string;
  type?: string;
  organizationId: string;
}

export interface LinkedInProfile {
  id: string;
  name: string;
  organizations: LinkedInOrganization[];
}

export function useLinkedInOAuth() {
  const [isLoading, setIsLoading] = useState(false);
  const [linkedInProfile, setLinkedInProfile] = useState<LinkedInProfile | null>(null);
  const { toast } = useToast();
  const { user, refreshSession } = useAuth();

  const generateCSRFToken = useCallback(() => {
    return Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
  }, []);

  const startLinkedInAuth = useCallback(() => {
    if (!user) {
      toast({
        title: "Authentication Required",
        description: "You must be logged in to connect LinkedIn",
        variant: "destructive",
      });
      return;
    }

    const csrfToken = generateCSRFToken();
    localStorage.setItem('linkedin_csrf_token', csrfToken);
    
    // Use the current page URL for redirect
    const redirectUri = `${window.location.origin}${window.location.pathname}`;
    
    const authUrl = `https://www.linkedin.com/oauth/v2/authorization?response_type=code&client_id=78o2mnkne785qf&redirect_uri=${encodeURIComponent(redirectUri)}&state=${csrfToken}&scope=openid%20profile%20email%20w_member_social%20rw_organization_admin`;
    
    console.log("Starting LinkedIn OAuth with redirect URI:", redirectUri);
    window.location.href = authUrl;
  }, [user, generateCSRFToken, toast]);

  const handleLinkedInCallback = useCallback(async (code: string, state: string) => {
    if (!user) {
      console.error("No user found for LinkedIn callback");
      return false;
    }

    const savedToken = localStorage.getItem('linkedin_csrf_token');
    if (state !== savedToken) {
      toast({
        title: "Security Error",
        description: "Invalid state token. Please try again.",
        variant: "destructive",
      });
      return false;
    }

    setIsLoading(true);
    try {
      console.log("Processing LinkedIn OAuth callback with code:", code);
      
      // Call our edge function to handle the OAuth flow
      const { data, error } = await supabase.functions.invoke('linkedin-oauth', {
        body: { code, userId: user.id }
      });

      if (error) {
        console.error("LinkedIn OAuth edge function error:", error);
        throw new Error(error.message || "Failed to process LinkedIn OAuth");
      }

      if (!data.success) {
        console.error("LinkedIn OAuth failed:", data.error);
        throw new Error(data.error || "LinkedIn OAuth failed");
      }

      console.log("LinkedIn OAuth success:", data);
      setLinkedInProfile(data);
      localStorage.removeItem('linkedin_csrf_token');
      
      // Refresh the user session to update the linkedin_connected status
      await refreshSession();
      
      toast({
        title: "LinkedIn Connected",
        description: "Successfully connected to LinkedIn and retrieved your organizations.",
      });

      return true;
    } catch (error: any) {
      console.error("LinkedIn OAuth error:", error);
      toast({
        title: "Connection Failed",
        description: error.message || "Failed to connect to LinkedIn. Please try again.",
        variant: "destructive",
      });
      return false;
    } finally {
      setIsLoading(false);
    }
  }, [user, toast, refreshSession]);

  const loadLinkedInProfile = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      console.log("Loading LinkedIn profile for user:", user.id);
      
      // Check if user has LinkedIn token
      const { data: tokenData, error: tokenError } = await supabase
        .from('linkedin_tokens')
        .select('linkedin_user_id, access_token, expires_at')
        .eq('user_id', user.id)
        .single();

      if (tokenError || !tokenData) {
        console.log("No LinkedIn token found for user");
        setLinkedInProfile(null);
        return;
      }

      // Check if token is expired
      const now = new Date();
      const expiresAt = new Date(tokenData.expires_at);
      if (expiresAt <= now) {
        console.log("LinkedIn token expired");
        setLinkedInProfile(null);
        return;
      }

      console.log("Found valid LinkedIn token");

      // Get organizations
      const { data: orgsData, error: orgsError } = await supabase
        .from('linkedin_organizations')
        .select('*')
        .eq('user_id', user.id);

      if (orgsError) {
        console.error("Error loading organizations:", orgsError);
        throw orgsError;
      }

      const profile: LinkedInProfile = {
        id: tokenData.linkedin_user_id,
        name: "LinkedIn User", // We could store name separately if needed
        organizations: orgsData?.map(org => ({
          id: org.id,
          name: org.organization_name,
          type: org.organization_type,
          organizationId: org.organization_id
        })) || []
      };

      console.log("Loaded LinkedIn profile:", profile);
      setLinkedInProfile(profile);
    } catch (error) {
      console.error("Error loading LinkedIn profile:", error);
      setLinkedInProfile(null);
    } finally {
      setIsLoading(false);
    }
  }, [user]);

  const disconnectLinkedIn = useCallback(async () => {
    if (!user) return;

    try {
      setIsLoading(true);
      
      console.log("Disconnecting LinkedIn for user:", user.id);
      
      // Delete tokens and organizations
      const [tokenResult, orgsResult] = await Promise.all([
        supabase.from('linkedin_tokens').delete().eq('user_id', user.id),
        supabase.from('linkedin_organizations').delete().eq('user_id', user.id)
      ]);

      if (tokenResult.error) {
        console.error("Error deleting tokens:", tokenResult.error);
        throw tokenResult.error;
      }

      if (orgsResult.error) {
        console.error("Error deleting organizations:", orgsResult.error);
        throw orgsResult.error;
      }

      setLinkedInProfile(null);
      
      toast({
        title: "LinkedIn Disconnected",
        description: "Successfully disconnected from LinkedIn.",
      });
    } catch (error: any) {
      console.error("Error disconnecting LinkedIn:", error);
      toast({
        title: "Disconnection Failed",
        description: "Failed to disconnect from LinkedIn. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  }, [user, toast]);

  return {
    isLoading,
    linkedInProfile,
    startLinkedInAuth,
    handleLinkedInCallback,
    loadLinkedInProfile,
    disconnectLinkedIn,
  };
}
