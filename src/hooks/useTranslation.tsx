
import { usePostPreferences } from "@/context/PostPreferencesContext";
import translations, { TranslationKey } from "@/i18n/translations";
import { useEffect } from "react";

type TranslationOptions = {
  default?: string;
  replacements?: Record<string, string>;
};

export function useTranslation() {
  const { preferences } = usePostPreferences();
  const { language } = preferences;
  
  useEffect(() => {
    // Update the document language attribute when language changes
    document.documentElement.lang = language;
  }, [language]);

  const t = (key: TranslationKey | string, options?: TranslationOptions | Record<string, string>): string => {
    // Check if key is a valid string
    if (typeof key !== 'string') {
      console.warn('Invalid translation key:', key);
      return 'TRANSLATION_ERROR';
    }
    
    // Handle both formats: t(key, { replacements }) and t(key, { default, replacements })
    const isLegacyFormat = options && !('default' in options) && !('replacements' in options);
    const defaultValue = isLegacyFormat ? key : (options as TranslationOptions)?.default || key;
    const replacements = isLegacyFormat ? options as Record<string, string> : (options as TranslationOptions)?.replacements;

    // Get the current language translations
    const currentLanguageTranslations = translations[language] || {};
    const fallbackTranslations = translations.en || {};
    
    // Look for the translation in the current language or fallback to English
    let text: string | undefined;
    
    // Check if the key exists in our translations
    if (key in currentLanguageTranslations) {
      text = currentLanguageTranslations[key as TranslationKey];
    } else if (key in fallbackTranslations) {
      text = fallbackTranslations[key as TranslationKey];
      console.debug(`Using English fallback for "${key}" in language "${language}"`);
    } else {
      // Use the default value if provided
      if (defaultValue && defaultValue !== key) {
        text = defaultValue;
      } else {
        // Fallback to showing a more user-friendly value by converting the key
        // Transform "products.title" into "Products"
        const lastPart = key.split('.').pop() || key;
        const friendlyText = lastPart
          .replace(/([A-Z])/g, ' $1') // Add spaces before capital letters
          .replace(/_/g, ' ') // Replace underscores with spaces
          .replace(/\./g, ' ') // Replace dots with spaces
          .replace(/^./, str => str.toUpperCase()); // Capitalize first letter
        
        text = friendlyText;
      }
    }
    
    // Handle replacements if provided
    if (replacements && text) {
      Object.entries(replacements).forEach(([placeholder, value]) => {
        text = text!.replace(`{${placeholder}}`, value);
      });
    }
    
    return text || defaultValue; // Ensure we never return undefined/null
  };
  
  return { t, currentLanguage: language };
}
