import { useState, useEffect } from "react";
import { useToast } from "@/components/ui/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

export type AIAgentPreferences = {
  [key: string]: any;
};

export function useAIAgentPreferences(agentId: string) {
  const [preferences, setPreferences] = useState<AIAgentPreferences>({});
  const [loading, setLoading] = useState(false);
  const { toast } = useToast();
  const { user, isAuthenticated } = useAuth();

  useEffect(() => {
    if (isAuthenticated && user) {
      loadAgentPreferences();
    }
  }, [isAuthenticated, user, agentId]);

  const loadAgentPreferences = async () => {
    if (!user || !agentId) return;
    
    try {
      setLoading(true);
      
      const { data, error } = await supabase
        .from('ai_agent_preferences')
        .select('preferences')
        .eq('user_id', user.id)
        .eq('agent_id', agentId)
        .maybeSingle();
      
      if (error) throw error;
      
      if (data) {
        // Fix TypeScript error - ensure we're setting an object
        if (typeof data.preferences === 'object' && data.preferences !== null) {
          setPreferences(data.preferences as AIAgentPreferences);
        } else {
          setPreferences({});
        }
      }
    } catch (error) {
      console.error("Error loading AI agent preferences:", error);
      toast({
        title: "Error loading preferences",
        description: "Could not load your preferences. Using defaults.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const updatePreferences = (newPreferences: Partial<AIAgentPreferences>) => {
    setPreferences((prev) => ({ ...prev, ...newPreferences }));
  };

  const savePreferences = async () => {
    try {
      setLoading(true);
      
      if (!user || !agentId) {
        toast({
          title: "Error saving preferences",
          description: "You must be logged in to save preferences.",
          variant: "destructive",
        });
        return Promise.reject(new Error("Authentication required"));
      }
      
      // Save to Supabase
      const { error } = await supabase
        .from('ai_agent_preferences')
        .upsert({
          user_id: user.id,
          agent_id: agentId,
          preferences: preferences,
          updated_at: new Date().toISOString()
        }, { onConflict: 'user_id,agent_id' });
      
      if (error) throw error;
      
      toast({
        title: "Preferences saved",
        description: "Your preferences have been updated successfully.",
      });
      
      return Promise.resolve();
    } catch (error: any) {
      toast({
        title: "Error saving preferences",
        description: error.message || "Please try again later.",
        variant: "destructive",
      });
      console.error("Save AI agent preferences error:", error);
      return Promise.reject(error);
    } finally {
      setLoading(false);
    }
  };

  return {
    preferences,
    updatePreferences,
    savePreferences,
    loading,
  };
}
