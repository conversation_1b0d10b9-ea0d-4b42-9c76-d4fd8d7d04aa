
import { StrictMode } from 'react';
import { createRoot } from 'react-dom/client';
import App from './App.tsx';
import './index.css';

// Function to initialize the application
const initApp = () => {
  const container = document.getElementById("root");
  
  if (!container) {
    console.error("Root element not found. App cannot be mounted.");
    return;
  }
  
  // Create root with error boundary
  try {
    const root = createRoot(container);
    
    root.render(
      <StrictMode>
        <App />
      </StrictMode>
    );
    
    console.log("Application successfully mounted to DOM");
  } catch (error) {
    console.error("Failed to render application:", error);
  }
};

// Add performance tracking
document.addEventListener('visibilitychange', () => {
  if (document.visibilityState === 'visible') {
    performance.mark('visible');
  }
});

// Ensure DOM is ready before initializing
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initApp);
} else {
  initApp();
}

// Register additional metrics after load
window.addEventListener('load', () => {
  // Use requestIdleCallback to measure after the page is interactive
  if ('requestIdleCallback' in window) {
    requestIdleCallback(() => {
      performance.mark('time-to-interactive');
    });
  }
});
