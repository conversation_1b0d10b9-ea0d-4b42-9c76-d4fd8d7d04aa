
// This file is kept for backward compatibility but now uses the useLinkedInOAuth hook
import { useLinkedInOAuth } from "@/hooks/useLinkedInOAuth";

/**
 * @deprecated Use useLinkedInOAuth hook instead
 */
export const initiateLinkedInAuth = async (): Promise<void> => {
  console.warn("initiateLinkedInAuth is deprecated. Use useLinkedInOAuth hook instead.");
  throw new Error("Use useLinkedInOAuth hook instead");
};

/**
 * @deprecated Use useLinkedInOAuth hook instead
 */
export const handleLinkedInCallback = async (): Promise<boolean> => {
  console.warn("handleLinkedInCallback is deprecated. Use useLinkedInOAuth hook instead.");
  return false;
};
