
// This file is automatically generated. Do not edit it directly.
import { createClient } from '@supabase/supabase-js';
import type { Database } from './types';

const SUPABASE_URL = "https://jgupgbgfxvhhzitftjzi.supabase.co";
const SUPABASE_PUBLISHABLE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImpndXBnYmdmeHZoaHppdGZ0anppIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDcwNTM5NTQsImV4cCI6MjA2MjYyOTk1NH0.T6EOZ49GXtoAclMM2nUbIVYrgFKmtE2sjcwEMev7G8s";

// Import the supabase client like this:
// import { supabase } from "@/integrations/supabase/client";

export const supabase = createClient<Database>(SUPABASE_URL, SUPABASE_PUBLISHABLE_KEY);
