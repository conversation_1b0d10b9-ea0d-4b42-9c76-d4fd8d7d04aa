
import { BrowserRouter, Routes, Route } from "react-router-dom";
import { Suspense, lazy } from "react";
import { Skeleton } from "@/components/ui/skeleton";
import { AuthProvider } from "@/context/AuthContext";
import { PostPreferencesProvider } from "@/context/PostPreferencesContext";
import { Toaster } from "@/components/ui/toaster";
import { SubscriptionProvider } from "@/hooks/useSubscription";
import ErrorBoundary from "@/components/ErrorBoundary";

// Lazy load routes with prefetch hints to improve performance
const Index = lazy(() => import("@/pages/Index"));
const Login = lazy(() => import("@/pages/Login"));
const Signup = lazy(() => import("@/pages/Signup"));
const VerifyEmail = lazy(() => import("@/pages/VerifyEmail"));
const Dashboard = lazy(() => import("@/pages/Dashboard"));
const About = lazy(() => import("@/pages/About"));
const Pricing = lazy(() => import("@/pages/Pricing"));
const AIAgents = lazy(() => import("@/pages/AIAgents"));
const Settings = lazy(() => import("@/pages/Settings"));
const ThoughtLeader = lazy(() => import("@/pages/ThoughtLeader"));
const NotFound = lazy(() => import("@/pages/NotFound"));

// Import Navbar after defining it's wrapped by providers
const Navbar = lazy(() => import("@/components/Navbar"));

// Enhanced loading fallback component with better performance indicators
const PageSkeleton = () => (
  <div className="w-full max-w-7xl mx-auto p-4 md:p-6 animate-pulse">
    <Skeleton className="h-12 w-3/4 mb-8" />
    <Skeleton className="h-64 w-full mb-6" />
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <Skeleton className="h-32 w-full" />
      <Skeleton className="h-32 w-full" />
    </div>
  </div>
);

function App() {
  return (
    <ErrorBoundary>
      <BrowserRouter>
        <AuthProvider>
          <SubscriptionProvider>
            <PostPreferencesProvider>
              <Suspense fallback={<PageSkeleton />}>
                <Navbar />
              </Suspense>
              <main className="min-h-screen bg-background">
                <ErrorBoundary fallback={<div className="p-8 text-center">Failed to load page content</div>}>
                  <Suspense fallback={<PageSkeleton />}>
                    <Routes>
                      <Route path="/" element={<Index />} />
                      <Route path="/login" element={<Login />} />
                      <Route path="/signup" element={<Signup />} />
                      <Route path="/verify-email" element={<VerifyEmail />} />
                      <Route path="/dashboard" element={<Dashboard />} />
                      <Route path="/about" element={<About />} />
                      <Route path="/pricing" element={<Pricing />} />
                      <Route path="/ai-agents" element={<AIAgents />} />
                      <Route path="/settings" element={<Settings />} />
                      <Route path="/thought-leader" element={<ThoughtLeader />} />
                      <Route path="*" element={<NotFound />} />
                    </Routes>
                  </Suspense>
                </ErrorBoundary>
              </main>
              <Toaster />
            </PostPreferencesProvider>
          </SubscriptionProvider>
        </AuthProvider>
      </BrowserRouter>
    </ErrorBoundary>
  );
}

export default App;
