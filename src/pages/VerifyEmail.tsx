
import { useState, useEffect } from "react";
import { useNavigate, useLocation } from "react-router-dom";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { toast } from "@/hooks/use-toast";
import { Mail, CheckCircle, AlertTriangle, ArrowRight } from "lucide-react";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/context/AuthContext";

const VerifyEmail = () => {
  const [email, setEmail] = useState<string>("");
  const [isResending, setIsResending] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { loading } = useAuth();

  useEffect(() => {
    // Get email from location state or query params
    const searchParams = new URLSearchParams(location.search);
    const emailParam = searchParams.get("email");
    const stateEmail = location.state?.email;
    
    if (stateEmail) {
      setEmail(stateEmail);
    } else if (emailParam) {
      setEmail(emailParam);
    }
  }, [location]);

  const handleResendEmail = async () => {
    if (!email) {
      toast({
        title: "Email Required",
        description: "Please provide an email address to resend the verification.",
        variant: "destructive"
      });
      return;
    }

    setIsResending(true);
    try {
      const { error } = await supabase.auth.resend({
        type: 'signup',
        email: email,
      });

      if (error) throw error;

      toast({
        title: "Verification Email Sent",
        description: "We've sent a new verification email to your address.",
      });
    } catch (error: any) {
      console.error("Error resending verification email:", error);
      toast({
        title: "Failed to Resend",
        description: error.message || "Could not resend verification email. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsResending(false);
    }
  };

  const goToLogin = () => {
    navigate("/login", { state: { email } });
  };

  const goToSignup = () => {
    navigate("/signup");
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4 gradient-bg">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <div className="flex justify-center mb-4">
            <div className="rounded-full bg-blue-50 p-3">
              <Mail className="h-10 w-10 text-blue-500" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">Verify Your Email</CardTitle>
          <CardDescription className="text-center">
            We've sent a verification email to:
          </CardDescription>
          <p className="text-center font-medium">{email || "your email address"}</p>
        </CardHeader>

        <CardContent className="space-y-4">
          <div className="bg-amber-50 border border-amber-200 rounded-md p-4">
            <div className="flex items-start">
              <AlertTriangle className="h-5 w-5 text-amber-500 mr-2 mt-0.5 flex-shrink-0" />
              <p className="text-sm text-amber-800">
                Please check your email inbox and click the verification link to complete your registration.
                If you don't see the email, check your spam folder.
              </p>
            </div>
          </div>

          <div className="space-y-2">
            <h3 className="text-sm font-medium">What to do next?</h3>
            <ul className="space-y-3">
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Check your email inbox for the verification link</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Click the link in the email to verify your account</span>
              </li>
              <li className="flex items-start">
                <CheckCircle className="h-5 w-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
                <span className="text-sm">Return to the login page to sign in</span>
              </li>
            </ul>
          </div>
        </CardContent>

        <CardFooter className="flex flex-col space-y-3">
          <Button 
            onClick={handleResendEmail} 
            className="w-full" 
            variant="outline"
            disabled={isResending || loading}
          >
            {isResending ? "Sending..." : "Resend Verification Email"}
          </Button>
          
          <div className="flex justify-between w-full gap-2">
            <Button
              onClick={goToSignup}
              variant="ghost"
              className="flex-1"
            >
              Try Different Email
            </Button>
            
            <Button
              onClick={goToLogin}
              className="flex-1 gap-1"
            >
              Go to Login
              <ArrowRight className="h-4 w-4" />
            </Button>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default VerifyEmail;
