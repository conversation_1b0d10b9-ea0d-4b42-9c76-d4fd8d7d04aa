
import { useState, useC<PERSON>back, memo, useMemo } from "react";
import { <PERSON>, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, Apple, Linkedin } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";
import { toast } from "@/hooks/use-toast";

// Optimized Google icon component with memoization
const GoogleIcon = memo(() => (
  <svg 
    xmlns="http://www.w3.org/2000/svg" 
    height="24" 
    viewBox="0 0 24 24" 
    width="24"
    className="h-5 w-5"
    aria-hidden="true"
  >
    <path d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z" fill="#4285F4"/>
    <path d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z" fill="#34A853"/>
    <path d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z" fill="#FBBC05"/>
    <path d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z" fill="#EA4335"/>
    <path d="M1 1h22v22H1z" fill="none"/>
  </svg>
));

// Optimized divider component with memoization
const Divider = memo(() => (
  <div className="relative">
    <div className="absolute inset-0 flex items-center">
      <span className="w-full border-t" />
    </div>
    <div className="relative flex justify-center text-xs uppercase">
      <span className="bg-background px-2 text-muted-foreground">
        or continue with
      </span>
    </div>
  </div>
));

const Login = () => {
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const { signIn, signInWithGoogle, signInWithApple, signInWithLinkedIn, loading } = useAuth();
  const navigate = useNavigate();
  const { t } = useTranslation();

  // Memoize handlers to prevent unnecessary re-renders
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!email || !password) return;
    
    try {
      await signIn(email, password);
      navigate("/dashboard");
    } catch (error) {
      console.error("Login error:", error);
      toast({
        title: "Sign In Failed",
        description: "Invalid email or password. Please try again.",
        variant: "destructive"
      });
    }
  }, [email, password, signIn, navigate]);

  const handleGoogleSignIn = useCallback(async () => {
    try {
      await signInWithGoogle();
      // Redirect is handled by Supabase OAuth flow
    } catch (error) {
      console.error("Google sign in error:", error);
      toast({
        title: "Connection Failed",
        description: "Could not connect to Google. Please check your network connection and try again.",
        variant: "destructive"
      });
    }
  }, [signInWithGoogle]);

  const handleAppleSignIn = useCallback(async () => {
    try {
      await signInWithApple();
      // Redirect is handled by Supabase OAuth flow
    } catch (error) {
      console.error("Apple sign in error:", error);
      toast({
        title: "Connection Failed",
        description: "Could not connect to Apple. Please try again.",
        variant: "destructive"
      });
    }
  }, [signInWithApple]);

  const handleLinkedInSignIn = useCallback(async () => {
    try {
      await signInWithLinkedIn();
      // Redirect is handled by Supabase OAuth flow
    } catch (error) {
      console.error("LinkedIn sign in error:", error);
      toast({
        title: "Connection Failed",
        description: "Could not connect to LinkedIn. Please try again.",
        variant: "destructive"
      });
    }
  }, [signInWithLinkedIn]);

  // Use CSS containment for better paint performance
  return (
    <div className="min-h-screen flex items-center justify-center px-4 gradient-bg contain-layout will-change-transform">
      <Card className="w-full max-w-md contain-content">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold">{t("signIn")}</CardTitle>
          <CardDescription>
            {t("enterEmailPassword")}
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-4">
            <Button 
              variant="outline" 
              className="w-full flex justify-center items-center gap-2"
              onClick={handleGoogleSignIn}
              disabled={loading}
            >
              <GoogleIcon />
              {t("continueWithGoogle")}
            </Button>
            <Button 
              variant="outline" 
              className="w-full flex justify-center items-center gap-2"
              onClick={handleAppleSignIn}
              disabled={loading}
            >
              <Apple className="h-5 w-5" />
              {t("continueWithApple")}
            </Button>
            <Button 
              variant="outline" 
              className="w-full flex justify-center items-center gap-2"
              onClick={handleLinkedInSignIn}
              disabled={loading}
            >
              <Linkedin className="h-5 w-5 text-blue-600" />
              Continue with LinkedIn
            </Button>
          </div>

          <Divider />

          <form onSubmit={handleSubmit}>
            <div className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="email">{t("email")}</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password">{t("password")}</Label>
                  <Link
                    to="/forgot-password"
                    className="text-sm text-linkedin-blue hover:underline"
                  >
                    {t("forgotPassword")}
                  </Link>
                </div>
                <Input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  required
                />
              </div>
              <Button
                type="submit"
                className="w-full bg-linkedin-blue hover:bg-linkedin-darkblue"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t("pleaseWait")}
                  </>
                ) : (
                  t("signIn")
                )}
              </Button>
            </div>
          </form>
        </CardContent>
        <CardFooter className="flex justify-center">
          <div className="text-center text-sm">
            {t("dontHaveAccount")}{" "}
            <Link to="/signup" className="text-linkedin-blue hover:underline">
              {t("signUp")}
            </Link>
          </div>
        </CardFooter>
      </Card>
    </div>
  );
};

export default Login;
