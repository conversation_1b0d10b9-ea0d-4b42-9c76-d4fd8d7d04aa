import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Loader2 } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "@/hooks/useTranslation";

const Pricing = () => {
  const {
    user,
    isAuthenticated
  } = useAuth();
  const [loadingPlan, setLoadingPlan] = useState<string | null>(null);
  const navigate = useNavigate();
  const {
    toast
  } = useToast();
  const {
    t
  } = useTranslation();

  const plans = [{
    name: "Basic",
    price: "$4.99",
    description: t("basicPlanDescription"),
    features: [t("sevenDayFreeTrial"), t("onePostPerDay"), t("fiveTopicsMaximum"), t("linkedinAutomaticPosting"), t("facebookBasicIntegration"), t("instagramBasicIntegration"), t("standardAiContentGeneration"), t("basicAnalytics")],
    recommended: false,
    stripePriceId: "price_basic123",
    hasTrial: true
  }, {
    name: "Premium",
    price: "$9.99",
    description: t("premiumPlanDescription"),
    features: [t("threePostsPerDay"), t("unlimitedTopics"), t("linkedinAdvancedIntegration"), t("facebookFullIntegration"), t("instagramFullIntegration"), t("advancedAiContentGeneration"), t("contentCalendar"), t("detailedAnalytics"), t("emailSupport")],
    recommended: true,
    stripePriceId: "price_premium456",
    hasTrial: false
  }];

  const handleSelectPlan = async (planName: string, priceId: string, hasTrial: boolean) => {
    if (!isAuthenticated) {
      // Redirect to signup if not authenticated
      navigate("/signup");
      return;
    }
    setLoadingPlan(planName);
    try {
      // This is a placeholder - in a real implementation you would call your Stripe checkout API
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Mock successful subscription
      const successMessage = hasTrial ? t("subscriptionWithTrialStarted", {
        planName
      }) : t("subscriptionStarted", {
        planName
      });
      toast({
        title: t("subscriptionUpdated"),
        description: successMessage
      });

      // In real implementation, redirect to Stripe Checkout page
      // window.location.href = stripeCheckoutUrl;

      // For now, redirect to dashboard
      navigate("/dashboard");
    } catch (error) {
      console.error("Subscription error:", error);
      toast({
        title: t("subscriptionError"),
        description: t("subscriptionErrorDescription"),
        variant: "destructive"
      });
    } finally {
      setLoadingPlan(null);
    }
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t("simpleTransparentPricing")}</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          {t("chooseRightPlan")}
        </p>
      </div>

      <div className="grid md:grid-cols-2 gap-8 max-w-4xl mx-auto">
        {plans.map(plan => {
          const isCurrentPlan = user?.subscription?.plan?.toLowerCase() === plan.name.toLowerCase() && user?.subscription?.status === 'active';
          return (
            <Card key={plan.name} className={`flex flex-col ${plan.recommended ? 'border-linkedin-blue ring-2 ring-linkedin-blue/20' : ''}`}>
              {plan.recommended}
              <CardHeader>
                <CardTitle className="text-2xl">{plan.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-3xl font-bold">{plan.price}</span>
                  <span className="text-gray-500 ml-1">/month</span>
                </div>
                <CardDescription className="mt-2">{plan.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-3">
                  {plan.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                {isCurrentPlan ? (
                  <Button disabled className="w-full bg-green-600 hover:bg-green-700">
                    <Check className="mr-2 h-4 w-4" /> {t("currentPlan")}
                  </Button>
                ) : (
                  <Button 
                    onClick={() => handleSelectPlan(plan.name, plan.stripePriceId, plan.hasTrial)} 
                    disabled={loadingPlan === plan.name} 
                    className={`w-full ${plan.recommended ? 'bg-linkedin-blue hover:bg-linkedin-darkblue' : ''}`}
                  >
                    {loadingPlan === plan.name ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t("processing")}
                      </>
                    ) : plan.hasTrial ? (
                      t("startSevenDayFreeTrial")
                    ) : (
                      t("subscribeToPlan")
                    )}
                  </Button>
                )}
              </CardFooter>
            </Card>
          );
        })}
      </div>
      
      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">{t("frequentlyAskedQuestions")}</h2>
        <div className="max-w-3xl mx-auto space-y-6 text-left">
          <div>
            <h3 className="text-lg font-semibold mb-2">{t("howFreeTrialWorks")}</h3>
            <p className="text-gray-600">{t("freeTrialExplanation")}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">{t("canCancelAnytime")}</h3>
            <p className="text-gray-600">{t("cancelSubscriptionExplanation")}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">{t("howBillingWorks")}</h3>
            <p className="text-gray-600">{t("billingExplanation")}</p>
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-2">{t("canChangePlan")}</h3>
            <p className="text-gray-600">{t("changePlanExplanation")}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Pricing;
