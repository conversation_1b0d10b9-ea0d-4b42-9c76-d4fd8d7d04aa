import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { usePostPreferences } from "@/context/PostPreferencesContext";
import { LinkedInAuthenticator } from "@/components/LinkedInAuthenticator";
import { PostingPreferences } from "@/components/PostingPreferences";
import { ContentPreferences } from "@/components/ContentPreferences";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { CalendarClock, Check, AlertTriangle } from "lucide-react";
import { SocialConnectors } from "@/components/SocialConnectors";

const Dashboard = () => {
  const { user, isAuthenticated, loading } = useAuth();
  const { preferences } = usePostPreferences();
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState("overview");

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }
  }, [loading, isAuthenticated, navigate]);

  // Mock function to calculate next post date and time
  const getNextPostDate = () => {
    const now = new Date();
    const [hours, minutes] = preferences.timeOfDay.split(":").map(Number);
    let tomorrow = new Date(now);
    tomorrow.setDate(tomorrow.getDate() + 1);
    tomorrow.setHours(hours, minutes, 0, 0);
    return tomorrow.toLocaleString("en-US", {
      weekday: "long",
      month: "long",
      day: "numeric",
      hour: "numeric",
      minute: "numeric",
      hour12: true,
    });
  };

  if (loading || !isAuthenticated) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  const isSocialConnected = user?.linkedin_connected || false;

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Dashboard</h1>
      
      <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab}>
        <TabsList className="grid w-full grid-cols-2 lg:w-[300px]">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="preferences">Preferences</TabsTrigger>
        </TabsList>
        
        <TabsContent value="overview" className="space-y-6 mt-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Account Status</CardTitle>
                <CardDescription>Your current subscription and status</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex items-center space-x-2">
                    <div className={`w-3 h-3 rounded-full ${user?.subscription?.status === 'active' ? 'bg-green-500' : 'bg-yellow-500'}`}></div>
                    <span className="font-medium">
                      {user?.subscription?.status === 'active' ? 'Active' : 'Free Trial'}
                    </span>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Current Plan:</span>
                    <p className="font-medium capitalize">{user?.subscription?.plan || "Free"}</p>
                  </div>
                  <div>
                    <span className="text-sm text-gray-500">Trial Ends:</span>
                    <p className="font-medium">May 18, 2025</p>
                  </div>
                  {user?.subscription?.status !== 'active' && (
                    <Button 
                      onClick={() => navigate("/pricing")}
                      className="w-full mt-2 bg-linkedin-blue hover:bg-linkedin-darkblue"
                    >
                      Upgrade Now
                    </Button>
                  )}
                </div>
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Social Media Connections</CardTitle>
                <CardDescription>Your connected social accounts</CardDescription>
              </CardHeader>
              <CardContent>
                {isSocialConnected ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2 text-green-600">
                      <Check className="h-5 w-5" />
                      <span className="font-medium">1 account connected</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      Your posts will be automatically published to your connected social media accounts.
                    </p>
                    <Button 
                      onClick={() => setActiveTab("preferences")} 
                      variant="outline" 
                      className="w-full"
                    >
                      Manage Connections
                    </Button>
                  </div>
                ) : (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2 text-yellow-600">
                      <AlertTriangle className="h-5 w-5" />
                      <span className="font-medium">No Accounts Connected</span>
                    </div>
                    <p className="text-sm text-gray-500 mb-3">
                      Connect your social media accounts to enable automatic posting.
                    </p>
                    <Button 
                      onClick={() => setActiveTab("preferences")} 
                      className="w-full"
                    >
                      Connect Accounts
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
            
            <Card>
              <CardHeader className="pb-2">
                <CardTitle>Next Scheduled Post</CardTitle>
                <CardDescription>When your next post will be published</CardDescription>
              </CardHeader>
              <CardContent>
                {preferences.daysOfWeek.length > 0 && isSocialConnected ? (
                  <div className="space-y-4">
                    <div className="flex items-center space-x-2">
                      <CalendarClock className="h-5 w-5 text-linkedin-blue" />
                      <span className="font-medium">{getNextPostDate()}</span>
                    </div>
                    <p className="text-sm text-gray-500">
                      Content mode: <span className="font-medium">
                        {preferences.contentMode === "rss_with_ai" 
                          ? "RSS Feed with AI Enhancement" 
                          : "AI-Generated Content"}
                      </span>
                    </p>
                    {preferences.topics.length > 0 && (
                      <div className="text-sm text-gray-500">
                        <span className="font-medium">Topics:</span> {preferences.topics.join(", ")}
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-yellow-600">
                    <p className="font-medium mb-2">No posts scheduled</p>
                    <p className="text-sm text-gray-500">
                      {!isSocialConnected 
                        ? "Connect at least one social media account and set up your posting preferences." 
                        : "Set up your posting preferences to schedule your first post."}
                    </p>
                    <Button 
                      onClick={() => setActiveTab("preferences")} 
                      variant="outline" 
                      className="mt-3 w-full"
                    >
                      Set Preferences
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
          
          {!isSocialConnected && (
            <Alert variant="default" className="bg-yellow-50 border-yellow-200">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertTitle>Action Required</AlertTitle>
              <AlertDescription>
                Please connect at least one social media account to enable automatic posting. Go to the Preferences tab to connect.
              </AlertDescription>
            </Alert>
          )}
          
          {preferences.rssFeeds.length === 0 && preferences.contentMode === "rss_with_ai" && (
            <Alert variant="default" className="bg-yellow-50 border-yellow-200">
              <AlertTriangle className="h-4 w-4 text-yellow-600" />
              <AlertTitle>RSS Topic Missing</AlertTitle>
              <AlertDescription>
                You've selected RSS feed mode but haven't entered a topic yet. Go to the Preferences tab to add a topic for your RSS feed.
              </AlertDescription>
            </Alert>
          )}
        </TabsContent>
        
        <TabsContent value="preferences" className="mt-6">
          <Card>
            <CardHeader>
              <CardTitle>Posting & Content Preferences</CardTitle>
              <CardDescription>Configure when, where, and what content to post on social media</CardDescription>
            </CardHeader>
            <CardContent>
              <PostingPreferences />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default Dashboard;
