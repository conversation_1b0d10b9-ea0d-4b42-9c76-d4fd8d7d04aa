
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { useTranslation } from "@/hooks/useTranslation";

const About = () => {
  const { t } = useTranslation();
  
  return (
    <div className="container mx-auto px-4 py-12">
      <div className="max-w-3xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">{t("about")}</h1>
        
        <div className="prose max-w-none">
          <p className="text-xl text-gray-600 mb-8">
            {t("aboutDescription", { default: "SocialPoster was built to solve a common problem for busy professionals: staying active on social media without spending hours creating content." })}
          </p>
          
          <h2 className="text-2xl font-semibold mt-12 mb-4">{t("ourMission", { default: "Our Mission" })}</h2>
          <p className="mb-6">
            {t("missionDescription", { default: "We believe that maintaining a professional presence on social media is essential for career growth and networking, but we also understand that consistently creating quality content takes time. Our mission is to help professionals stay active and engaged on social media without the daily time commitment." })}
          </p>
          
          <h2 className="text-2xl font-semibold mt-12 mb-4">{t("howItWorks", { default: "How It Works" })}</h2>
          <p className="mb-6">
            {t("howItWorksDescription", { default: "SocialPoster uses advanced AI to generate professional, relevant content based on the topics and tone you specify. You set your preferences once - how often to post, what topics to focus on, and what tone to use - and our system handles the rest, automatically publishing posts to your social media profiles according to your schedule." })}
          </p>
          
          <div className="bg-gray-50 p-6 rounded-lg my-8">
            <h3 className="text-xl font-semibold mb-3">{t("keyFeatures", { default: "Key Features" })}</h3>
            <ul className="space-y-2 list-disc pl-6">
              <li>{t("keyFeature1", { default: "Fully automated social media posting" })}</li>
              <li>{t("keyFeature2", { default: "AI-generated content tailored to your professional niche" })}</li>
              <li>{t("keyFeature3", { default: "Customizable posting schedule and frequency" })}</li>
              <li>{t("keyFeature4", { default: "Topic and sentiment control" })}</li>
              <li>{t("keyFeature5", { default: "Simple setup process with social media authentication" })}</li>
              <li>{t("keyFeature6", { default: "Integration with Make.com for advanced workflows" })}</li>
            </ul>
          </div>
          
          <h2 className="text-2xl font-semibold mt-12 mb-4">{t("ourTechnology", { default: "Our Technology" })}</h2>
          <p className="mb-6">
            {t("technologyDescription", { default: "SocialPoster combines natural language processing, machine learning, and integration with social media APIs to create a seamless experience. We continuously refine our AI models to ensure the content generated is high-quality, relevant, and engaging." })}
          </p>
          
          <h2 className="text-2xl font-semibold mt-12 mb-4">{t("privacySecurity", { default: "Privacy & Security" })}</h2>
          <p className="mb-6">
            {t("privacyDescription", { default: "We take your data privacy seriously. SocialPoster only requests the minimum permissions needed to post on your behalf, and we never access or store your social media credentials. All communication with social platforms is done through their official OAuth authentication system." })}
          </p>
          
          <div className="mt-12 text-center">
            <h2 className="text-2xl font-semibold mb-6">{t("readyToAutomate", { default: "Ready to automate your social media presence?" })}</h2>
            <Button asChild size="lg" className="bg-linkedin-blue hover:bg-linkedin-darkblue">
              <Link to="/signup">{t("getStartedToday", { default: "Get Started Today" })}</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default About;
