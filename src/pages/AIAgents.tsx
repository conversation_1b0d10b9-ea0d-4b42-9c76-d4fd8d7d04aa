
import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Check, Loader2, Settings } from "lucide-react";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/use-toast";
import { useTranslation } from "@/hooks/useTranslation";
import { supabase } from "@/integrations/supabase/client";
import { useSubscription } from "@/hooks/useSubscription";

const AIAgents = () => {
  const { user, isAuthenticated } = useAuth();
  const { subscriptions, hasActiveSubscription, isLoading: subscriptionLoading, refreshSubscriptions } = useSubscription();
  const [loadingProduct, setLoadingProduct] = useState<string | null>(null);
  const navigate = useNavigate();
  const { toast } = useToast();
  const { t } = useTranslation();

  useEffect(() => {
    // Refresh subscription status when component mounts
    if (isAuthenticated && user) {
      refreshSubscriptions();
    }
  }, [isAuthenticated, user, refreshSubscriptions]);

  const products = [
    {
      id: "linkedin-thought-leader",
      name: t("products.linkedinThoughtLeader.title", { default: "LinkedIn Thought Leader" }),
      price: "$4.99",
      description: t("products.linkedinThoughtLeader.description", { default: "Establish yourself as a thought leader on LinkedIn with AI-generated content" }),
      features: [
        t("products.linkedinThoughtLeader.features.dailyPosts", { default: "Daily posts to your LinkedIn profile" }),
        t("products.linkedinThoughtLeader.features.topicSelection", { default: "Topic selection based on your industry" }),
        t("products.linkedinThoughtLeader.features.professionalTone", { default: "Professional tone customization" }),
        t("products.linkedinThoughtLeader.features.sentimentControl", { default: "Sentiment control" }),
        t("products.linkedinThoughtLeader.features.articleSharing", { default: "Article sharing capability" }),
        t("products.linkedinThoughtLeader.features.imageSelection", { default: "AI image selection" }),
        t("products.linkedinThoughtLeader.features.analytics", { default: "Performance analytics" })
      ],
      stripePriceId: "linkedin-thought-leader",
      hasTrial: true,
      managePath: "/thought-leader"
    },
    {
      id: "oneday-products",
      name: t("products.onedayProducts.title", { default: "1Day Products - Daily Content" }),
      price: "$39.99",
      description: t("products.onedayProducts.description", { default: "Showcase your 1Day product portfolio with AI-generated creative posts" }),
      features: [
        t("products.onedayProducts.features.dailyPosts", { default: "Daily posts to Instagram and Facebook" }),
        t("products.onedayProducts.features.randomSelection", { default: "Random product selection from your portfolio" }),
        t("products.onedayProducts.features.creativeContent", { default: "Creative and engaging content generation" }),
        t("products.onedayProducts.features.imageGeneration", { default: "Product image enhancement" }),
        t("products.onedayProducts.features.hashtagOptimization", { default: "Hashtag optimization for maximum reach" }),
        t("products.onedayProducts.features.scheduleControl", { default: "Posting schedule customization" }),
        t("products.onedayProducts.features.performance", { default: "Engagement tracking" })
      ],
      stripePriceId: "oneday-products",
      hasTrial: true,
      managePath: "/oneday-products"
    },
    {
      id: "travel-agency-poster",
      name: t("products.travelAgencyPoster.title", { default: "Travel Agency Last Minute Offer Poster" }),
      price: "$49.99",
      description: t("products.travelAgencyPoster.description", { default: "Automatically discover and post last-minute travel deals to your social media" }),
      features: [
        t("products.travelAgencyPoster.features.dealTracking", { default: "Monitors major travel agencies for deals" }),
        t("products.travelAgencyPoster.features.instantPosting", { default: "Automatic posting to Facebook and Instagram" }),
        t("products.travelAgencyPoster.features.contentGeneration", { default: "Generates compelling travel descriptions" }),
        t("products.travelAgencyPoster.features.imageSelection", { default: "Selects attractive destination images" }),
        t("products.travelAgencyPoster.features.customBranding", { default: "Adds your agency branding to posts" }),
        t("products.travelAgencyPoster.features.priceHighlighting", { default: "Highlights discounts and special offers" }),
        t("products.travelAgencyPoster.features.audienceTargeting", { default: "Customizable targeting for different audiences" })
      ],
      stripePriceId: "travel-agency-poster",
      hasTrial: false,
      managePath: "/travel-agency"
    }
  ];

  const handleSelectProduct = async (productId: string, hasTrial: boolean) => {
    if (!isAuthenticated) {
      navigate("/signup");
      return;
    }
    
    await startSubscription(productId);
  };

  const startSubscription = async (productId: string) => {
    setLoadingProduct(productId);
    
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication Error",
          description: "Please sign in to subscribe.",
          variant: "destructive"
        });
        setLoadingProduct(null);
        return;
      }

      const origin = window.location.origin;

      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: {
          productId,
          origin
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) throw error;

      window.location.href = data.url;
    } catch (error) {
      console.error("Subscription error:", error);
      toast({
        title: "Subscription Error",
        description: "There was a problem processing your subscription. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoadingProduct(null);
    }
  };

  const handleManageProduct = (product: typeof products[0]) => {
    navigate(product.managePath);
  };

  return (
    <div className="container mx-auto px-4 py-12">
      <div className="text-center mb-12">
        <h1 className="text-4xl font-bold mb-4">{t("products.title", { default: "AI Agents" })}</h1>
        <p className="text-xl text-gray-600 max-w-2xl mx-auto">
          {t("products.subtitle", { default: "Automate your social media presence with our AI-powered solutions" })}
        </p>
      </div>

      <div className="grid md:grid-cols-3 gap-8 max-w-6xl mx-auto">
        {products.map(product => {
          const isSubscribed = hasActiveSubscription(product.stripePriceId);
          
          return (
            <Card key={product.id} className="flex flex-col border-linkedin-blue shadow-md hover:shadow-lg transition-shadow">
              <CardHeader>
                <CardTitle className="text-2xl">{product.name}</CardTitle>
                <div className="mt-4">
                  <span className="text-3xl font-bold">{product.price}</span>
                  <span className="text-gray-500 ml-1">/{t("pricing.monthly", { default: "monthly" }).toLowerCase()}</span>
                </div>
                <CardDescription className="mt-2 text-base">{product.description}</CardDescription>
              </CardHeader>
              <CardContent className="flex-grow">
                <ul className="space-y-3">
                  {product.features.map((feature, i) => (
                    <li key={i} className="flex items-start">
                      <Check className="h-5 w-5 text-green-500 mr-2 flex-shrink-0 mt-0.5" />
                      <span>{feature}</span>
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter className="flex flex-col gap-2">
                {isSubscribed ? (
                  <>
                    <div className="w-full p-3 bg-green-50 border border-green-200 rounded-md text-green-700 text-center mb-2">
                      ✓ Active Subscription
                    </div>
                    <Button 
                      onClick={() => handleManageProduct(product)}
                      className="w-full"
                      variant="outline"
                    >
                      <Settings className="mr-2 h-4 w-4" /> Manage Agent
                    </Button>
                  </>
                ) : (
                  <Button 
                    onClick={() => handleSelectProduct(product.stripePriceId, product.hasTrial)} 
                    disabled={loadingProduct === product.stripePriceId} 
                    className="w-full bg-linkedin-blue hover:bg-linkedin-darkblue"
                  >
                    {loadingProduct === product.stripePriceId ? (
                      <>
                        <Loader2 className="mr-2 h-4 w-4 animate-spin" /> {t("subscription.processing", { default: "Processing..." })}
                      </>
                    ) : product.hasTrial ? t("subscription.startFreeTrial", { default: "Start Free Trial" }) : t("subscription.subscribe", { default: "Subscribe" })}
                  </Button>
                )}
              </CardFooter>
            </Card>
          );
        })}
      </div>

      
      <div className="mt-16 text-center">
        <h2 className="text-2xl font-bold mb-4">{t("products.howItWorks", { default: "How It Works" })}</h2>
        <div className="max-w-3xl mx-auto space-y-6 text-left">
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold mb-2">{t("products.step1.title", { default: "Connect Your Social Accounts" })}</h3>
            <p className="text-gray-600">{t("products.step1.description", { default: "Link your social media profiles to our platform for seamless posting." })}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold mb-2">{t("products.step2.title", { default: "Choose Your Preferences" })}</h3>
            <p className="text-gray-600">{t("products.step2.description", { default: "Set your industry, tone, and posting frequency to match your professional style." })}</p>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-sm">
            <h3 className="text-lg font-semibold mb-2">{t("products.step3.title", { default: "Let AI Do the Work" })}</h3>
            <p className="text-gray-600">{t("products.step3.description", { default: "Our AI will generate and post content based on your preferences, building your online presence automatically." })}</p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AIAgents;
