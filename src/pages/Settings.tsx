import { useEffect, useState } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { LinkedInOAuthConnector } from "@/components/LinkedInOAuthConnector";
import { LinkedInProfile } from "@/hooks/useLinkedInOAuthFlow";

const Settings = () => {
  const { user, isAuthenticated, loading, signOut } = useAuth();
  const navigate = useNavigate();
  const [linkedInProfile, setLinkedInProfile] = useState<LinkedInProfile | null>(null);

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }
  }, [loading, isAuthenticated, navigate]);

  // Handle LinkedIn OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');
    const error = urlParams.get('error');

    if (error) {
      console.error('LinkedIn OAuth Error:', error);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      return;
    }

    if (code && state && user) {
      handleLinkedInCallback(code, state);
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, [user]);

  const handleLinkedInCallback = async (code: string, state: string) => {
    try {
      console.log('Processing LinkedIn OAuth callback...');
      
      // Get current user session
      const { supabase } = await import("@/integrations/supabase/client");
      const { data: { session } } = await supabase.auth.getSession();
      
      if (!session) {
        throw new Error('No valid session found');
      }

      // Call Supabase Edge Function
      const response = await fetch(`https://jgupgbgfxvhhzitftjzi.supabase.co/functions/v1/linkedin-oauth-callback`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${session.access_token}`,
        },
        body: JSON.stringify({ 
          code, 
          userId: user?.id 
        }),
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('Edge Function Error:', errorText, 'Status:', response.status);
        throw new Error(`Edge Function call failed: ${response.status} ${errorText}`);
      }

      const data = await response.json();
      
      if (!data.success) {
        console.error('LinkedIn OAuth failed:', data.error);
        throw new Error(data.error || 'LinkedIn OAuth failed');
      }

      console.log('LinkedIn OAuth successful:', data);
      
      // Update UI with success
      alert(`Successfully connected to LinkedIn as ${data.name}`);
      
    } catch (error: any) {
      console.error('LinkedIn OAuth callback error:', error);
      alert(`Failed to connect to LinkedIn: ${error.message}`);
    }
  };

  const handleLinkedInConnect = async () => {
    // Check for LinkedIn Client ID
    const linkedinClientId = process.env.NEXT_PUBLIC_LINKEDIN_CLIENT_ID;
    
    if (!linkedinClientId || linkedinClientId.trim() === '') {
      console.error('Frontend Error: NEXT_PUBLIC_LINKEDIN_CLIENT_ID is not configured.');
      alert('LinkedIn integration setup is incomplete. Please contact support.');
      return;
    }

    try {
      // Generate CSRF token
      const state = Math.random().toString(36).substring(2, 15) + Math.random().toString(36).substring(2, 15);
      sessionStorage.setItem('linkedin_oauth_state', state);
      
      // Construct redirect URI dynamically
      const redirectUri = `${window.location.origin}/settings`;
      
      // LinkedIn OAuth parameters
      const params = new URLSearchParams({
        response_type: 'code',
        client_id: linkedinClientId,
        redirect_uri: redirectUri,
        state: state,
        scope: 'openid profile email w_member_social rw_organization_admin'
      });

      // Redirect to LinkedIn authorization URL
      const authUrl = `https://www.linkedin.com/oauth/v2/authorization?${params.toString()}`;
      console.log('Redirecting to LinkedIn OAuth URL:', authUrl);
      window.location.href = authUrl;
      
    } catch (error: any) {
      console.error('Error starting LinkedIn OAuth:', error);
      alert(`Failed to start LinkedIn OAuth: ${error.message}`);
    }
  };

  const handleLinkedInConnectionChange = (profile: LinkedInProfile | null) => {
    setLinkedInProfile(profile);
  };

  if (loading || !isAuthenticated || !user) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-3xl font-bold mb-6">Account Settings</h1>
      
      <div className="grid gap-6 md:grid-cols-12">
        <div className="md:col-span-4 lg:col-span-3">
          <div className="space-y-4">
            <div className="font-semibold text-lg">Settings</div>
            <nav className="flex flex-col space-y-1">
              <a href="#profile" className="px-3 py-2 rounded-md bg-gray-100 font-medium">Profile</a>
              <a href="#subscription" className="px-3 py-2 rounded-md text-gray-600 hover:bg-gray-50">Subscription</a>
              <a href="#linkedin" className="px-3 py-2 rounded-md text-gray-600 hover:bg-gray-50">LinkedIn Connection</a>
              <a href="#notifications" className="px-3 py-2 rounded-md text-gray-600 hover:bg-gray-50">Notifications</a>
              <a href="#privacy" className="px-3 py-2 rounded-md text-gray-600 hover:bg-gray-50">Privacy & Security</a>
            </nav>
          </div>
        </div>
        
        <div className="md:col-span-8 lg:col-span-9 space-y-6">
          <Card id="profile">
            <CardHeader>
              <CardTitle>Profile Information</CardTitle>
              <CardDescription>Update your personal information</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="name">Full Name</Label>
                  <Input id="name" defaultValue={user.name || ""} />
                </div>
                <div className="space-y-2">
                  <Label htmlFor="email">Email Address</Label>
                  <Input id="email" defaultValue={user.email} disabled />
                  <p className="text-xs text-gray-500">Email cannot be changed</p>
                </div>
              </div>
              <div className="flex justify-end">
                <Button>Save Changes</Button>
              </div>
            </CardContent>
          </Card>

          <Card id="subscription">
            <CardHeader>
              <CardTitle>Subscription</CardTitle>
              <CardDescription>Manage your subscription plan</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-gray-50 p-4 rounded-md">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-medium">
                      Current Plan: <span className="capitalize">{user?.subscription?.plan || "Free"}</span>
                    </p>
                    <p className="text-sm text-gray-500">
                      Status: <span className="capitalize">{user?.subscription?.status || "inactive"}</span>
                    </p>
                  </div>
                  <Button variant="outline" onClick={() => navigate("/pricing")}>
                    {user?.subscription?.status === "active" ? "Change Plan" : "Upgrade"}
                  </Button>
                </div>
              </div>
              
              {user?.subscription?.status === "active" && (
                <div className="pt-2">
                  <Button variant="outline" className="text-destructive">
                    Cancel Subscription
                  </Button>
                  <p className="text-xs text-gray-500 mt-2">
                    You'll continue to have access until the end of your current billing period.
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          <Card id="linkedin">
            <CardHeader>
              <CardTitle>LinkedIn Connection</CardTitle>
              <CardDescription>Connect your LinkedIn account to enable posting</CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <Button 
                onClick={handleLinkedInConnect}
                className="w-full bg-blue-600 hover:bg-blue-700"
              >
                Connect LinkedIn
              </Button>
            </CardContent>
          </Card>

          <Card id="privacy">
            <CardHeader>
              <CardTitle>Privacy & Security</CardTitle>
              <CardDescription>Manage your password and account security</CardDescription>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h3 className="font-semibold mb-4">Change Password</h3>
                <div className="space-y-4">
                  <div className="space-y-2">
                    <Label htmlFor="current-password">Current Password</Label>
                    <Input id="current-password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="new-password">New Password</Label>
                    <Input id="new-password" type="password" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="confirm-password">Confirm New Password</Label>
                    <Input id="confirm-password" type="password" />
                  </div>
                  <div className="flex justify-end">
                    <Button>Update Password</Button>
                  </div>
                </div>
              </div>
              
              <Separator />
              
              <div>
                <h3 className="font-semibold mb-4 text-red-600">Danger Zone</h3>
                <p className="text-sm text-gray-500 mb-4">
                  Once you delete your account, there is no going back. Please be certain.
                </p>
                <Button variant="outline" className="text-destructive" onClick={signOut}>
                  Sign Out
                </Button>
                <Button variant="destructive" className="ml-4">
                  Delete Account
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
};

export default Settings;
