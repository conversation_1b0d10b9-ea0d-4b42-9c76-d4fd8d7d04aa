
import { useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { SubscriptionProvider } from "@/hooks/useSubscription";
import { LinkedInThoughtLeader } from "@/components/LinkedInThoughtLeader";

const ThoughtLeaderPage = () => {
  const { isAuthenticated, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/login");
    }
  }, [loading, isAuthenticated, navigate]);

  if (loading) {
    return <div className="min-h-screen flex items-center justify-center">Loading...</div>;
  }

  return (
    <div className="container mx-auto py-8">
      <h1 className="text-3xl font-bold mb-8 text-center">LinkedIn Thought Leader</h1>
      
      <SubscriptionProvider>
        <LinkedInThoughtLeader />
      </SubscriptionProvider>
    </div>
  );
};

export default ThoughtLeaderPage;
