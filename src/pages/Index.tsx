
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON> } from "react-router-dom";
import { useAuth } from "@/context/AuthContext";
import { ArrowRight, Clock, Calendar, MessageCircle, CheckCircle, Megaphone } from "lucide-react";
import { useTranslation } from "@/hooks/useTranslation";

const Index = () => {
  const { isAuthenticated } = useAuth();
  const { t } = useTranslation();

  // Define hardcoded translations for the missing items in the console logs
  const additionalFeatures = {
    articleSharing: t("products.linkedinThoughtLeader.features.articleSharing", { default: "Article sharing capability" }),
    imageSelection: t("products.linkedinThoughtLeader.features.imageSelection", { default: "AI image selection" }),
    analytics: t("products.linkedinThoughtLeader.features.analytics", { default: "Performance analytics" })
  };

  return (
    <div className="flex flex-col min-h-screen">
      <main className="flex-grow">
        {/* Hero Section */}
        <section className="bg-[#f0f4f8] py-28 md:py-40">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="max-w-4xl mx-auto">
              <h1 className="text-4xl md:text-6xl lg:text-7xl font-bold text-gray-800 mb-6 leading-tight">
                {t("socialMediaPresenceOnAutopilot")}
              </h1>
              <p className="text-xl text-gray-600 mb-12 max-w-2xl">
                {t("letAiCreateAndPost")}
              </p>
              <div className="flex flex-col sm:flex-row gap-4">
                {isAuthenticated ? (
                  <Button asChild size="lg" className="rounded-full bg-social-blue hover:bg-social-blue-dark text-lg px-8 py-6">
                    <Link to="/dashboard">
                      {t("goToDashboard")} <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                ) : (
                  <Button asChild size="lg" className="rounded-full bg-social-blue hover:bg-social-blue-dark text-lg px-8 py-6">
                    <Link to="/signup">
                      {t("trySocialPosterFree")}
                    </Link>
                  </Button>
                )}
              </div>
            </div>
          </div>
        </section>

        {/* Products Section */}
        <section className="py-24 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <div className="text-center mb-10">
              <h2 className="text-3xl font-bold text-gray-800 mb-4">{t("aiAgents", { default: "AI Agents" })}</h2>
              <p className="text-xl text-gray-600 max-w-2xl mx-auto">{t("products.subtitle", { default: "Automate your social media presence with our AI-powered solutions" })}</p>
            </div>
            
            <div className="max-w-5xl mx-auto">
              {/* LinkedIn Thought Leader Product */}
              <div className="bg-social-blue/10 rounded-2xl p-10 border border-social-blue/20">
                <h2 className="text-3xl font-bold text-gray-800 mb-6">{t("products.linkedinThoughtLeader.title", { default: "LinkedIn Thought Leader" })}</h2>
                <p className="text-gray-600 mb-8">
                  {t("products.linkedinThoughtLeader.description", { default: "Establish yourself as a thought leader on LinkedIn with AI-generated content" })}
                </p>
                <div className="flex items-baseline mb-8">
                  <span className="text-5xl font-bold text-gray-800">$4.99</span>
                  <span className="text-gray-500 ml-2">/ {t("pricing.monthly", { default: "monthly" }).toLowerCase()}</span>
                </div>
                <ul className="space-y-4 mb-8">
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{t("products.linkedinThoughtLeader.features.dailyPosts", { default: "Daily posts to your LinkedIn profile" })}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{t("products.linkedinThoughtLeader.features.topicSelection", { default: "Topic selection based on your industry" })}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{t("products.linkedinThoughtLeader.features.professionalTone", { default: "Professional tone customization" })}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{t("products.linkedinThoughtLeader.features.sentimentControl", { default: "Sentiment control" })}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{additionalFeatures.articleSharing}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{additionalFeatures.imageSelection}</span>
                  </li>
                  <li className="flex items-start">
                    <CheckCircle className="h-6 w-6 text-social-blue flex-shrink-0 mr-3 mt-0.5" />
                    <span className="text-gray-600">{additionalFeatures.analytics}</span>
                  </li>
                </ul>
                <div className="flex flex-col gap-4">
                  <Button asChild size="lg" className="w-full rounded-full bg-social-blue hover:bg-social-blue-dark text-white">
                    <Link to="/ai-agents">
                      {t("products.learnMore", { default: "Learn More" })}
                    </Link>
                  </Button>
                  <Button asChild variant="outline" size="lg" className="w-full rounded-full border-social-blue text-social-blue hover:bg-social-blue/10">
                    <Link to="/ai-agents">
                      {t("products.viewAllAgents", { default: "View All AI Agents" })} <ArrowRight className="ml-2 h-5 w-5" />
                    </Link>
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-24 bg-[#f0f4f8] text-center">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8">
            <h2 className="text-4xl font-bold text-center mb-4 text-gray-800">{t("aiPoweredSocialMediaPresence")}</h2>
            <p className="text-xl text-gray-600 mb-16 max-w-2xl mx-auto">
              {t("stayConsistentlyActive")}
            </p>
            
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-4xl mx-auto">
              <div className="flex flex-col items-center">
                <div className="h-16 w-16 flex items-center justify-center mb-4">
                  <Clock className="h-10 w-10 text-social-blue" />
                </div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">{t("timeSaving")}</h3>
                <p className="text-gray-600 text-sm">{t("reclaimHours")}</p>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="h-16 w-16 flex items-center justify-center mb-4">
                  <Calendar className="h-10 w-10 text-social-blue" />
                </div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">{t("consistency")}</h3>
                <p className="text-gray-600 text-sm">{t("neverMissPosting")}</p>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="h-16 w-16 flex items-center justify-center mb-4">
                  <MessageCircle className="h-10 w-10 text-social-blue" />
                </div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">{t("qualityContent")}</h3>
                <p className="text-gray-600 text-sm">{t("professionalAiWriting")}</p>
              </div>
              
              <div className="flex flex-col items-center">
                <div className="h-16 w-16 flex items-center justify-center mb-4">
                  <Megaphone className="h-10 w-10 text-social-blue" />
                </div>
                <h3 className="text-lg font-medium mb-2 text-gray-800">{t("multiPlatform")}</h3>
                <p className="text-gray-600 text-sm">{t("postToAllChannels")}</p>
              </div>
            </div>
            
            <div className="mt-16">
              <Button asChild size="lg" className="rounded-full bg-social-blue hover:bg-social-blue-dark text-lg px-8 py-6">
                <Link to="/signup">
                  {t("trySocialPosterFree")}
                </Link>
              </Button>
            </div>
          </div>
        </section>

        {/* Social Media Icons */}
        <section className="py-16 bg-white">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
            <div className="flex flex-wrap justify-center gap-8 items-center">
              <div className="flex items-center space-x-2">
                <div className="h-10 w-10 rounded-full bg-linkedin-blue flex items-center justify-center">
                  <span className="text-white font-bold">in</span>
                </div>
                <span className="text-gray-700 font-medium">{t("social.linkedin")}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="h-10 w-10 rounded-full bg-facebook-blue flex items-center justify-center">
                  <span className="text-white font-bold">f</span>
                </div>
                <span className="text-gray-700 font-medium">{t("social.facebook")}</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="h-10 w-10 rounded-full bg-gradient-to-br from-instagram-purple via-instagram-pink to-instagram-orange flex items-center justify-center">
                  <span className="text-white font-bold">Ig</span>
                </div>
                <span className="text-gray-700 font-medium">{t("social.instagram")}</span>
              </div>
            </div>
          </div>
        </section>

        {/* Testimonial Section */}
        <section className="py-24 bg-[#f0f4f8]">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-4xl">
            <blockquote className="text-center italic text-2xl sm:text-3xl text-gray-700 font-light">
              "{t("testimonialQuote")}"
            </blockquote>
            <p className="text-center mt-6 text-gray-500">{t("testimonialAuthor")}</p>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-24 bg-social-blue">
          <div className="container mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold mb-6 text-white">{t("readyToAutomate")}</h2>
            <p className="text-xl mb-12 max-w-2xl mx-auto text-white/80">
              {t("joinThousandsSaving")}
            </p>
            <Button asChild size="lg" variant="secondary" className="rounded-full bg-white text-social-blue hover:bg-gray-50 text-lg px-8 py-6">
              <Link to="/signup">{t("trySocialPosterFree")}</Link>
            </Button>
          </div>
        </section>
      </main>
      
      {/* Footer */}
      <footer className="bg-gray-800 text-white py-12">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">SocialPoster</h3>
              <p className="text-gray-400">
                {t("footerTagline")}
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">{t("quickLinks")}</h3>
              <ul className="space-y-2">
                <li><Link to="/" className="text-gray-400 hover:text-white">{t("home")}</Link></li>
                <li><Link to="/ai-agents" className="text-gray-400 hover:text-white">{t("products.title")}</Link></li>
                <li><Link to="/about" className="text-gray-400 hover:text-white">{t("about")}</Link></li>
                <li><Link to="/login" className="text-gray-400 hover:text-white">{t("signIn")}</Link></li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">{t("contact")}</h3>
              <p className="text-gray-400">
                {t("needHelp")}<br />
                <a href="mailto:<EMAIL>" className="hover:text-white"><EMAIL></a>
              </p>
            </div>
          </div>
          <div className="border-t border-gray-700 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; {new Date().getFullYear()} SocialPoster. {t("allRightsReserved")}</p>
          </div>
        </div>
      </footer>
    </div>
  );
};

export default Index;
