
import { useState } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { useScheduledWebhooks, DayOfWeek } from "@/hooks/useScheduledWebhooks";
import { Loader2, Clock, Webhook } from "lucide-react";

const daysOfWeek: { value: DayOfWeek; label: string }[] = [
  { value: "monday", label: "Monday" },
  { value: "tuesday", label: "Tuesday" },
  { value: "wednesday", label: "Wednesday" },
  { value: "thursday", label: "Thursday" },
  { value: "friday", label: "Friday" },
  { value: "saturday", label: "Saturday" },
  { value: "sunday", label: "Sunday" },
];

export function ScheduleWebhookForm() {
  const { createScheduledPost, loading } = useScheduledWebhooks();
  const [formData, setFormData] = useState({
    webhook_url: "https://hook.eu2.make.com/0tan058b2ctrnle06mgiyo2trm2dd1nb",
    scheduled_time: "09:00",
    days_of_week: ["monday", "wednesday", "friday"] as DayOfWeek[],
    sentiment: "professional",
    prompt: "",
    is_active: true,
    agent_id: "linkedin-thought-leader",
  });

  const handleDayToggle = (day: DayOfWeek, checked: boolean) => {
    if (checked) {
      setFormData(prev => ({
        ...prev,
        days_of_week: [...prev.days_of_week, day]
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        days_of_week: prev.days_of_week.filter(d => d !== day)
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.prompt.trim()) {
      return;
    }

    await createScheduledPost(formData);
    
    // Reset form
    setFormData(prev => ({
      ...prev,
      prompt: "",
    }));
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Clock className="h-5 w-5" />
          Schedule Automated Posts
        </CardTitle>
        <CardDescription>
          Set up automated posting to LinkedIn via Make.com webhook
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-2">
            <Label htmlFor="webhook_url" className="flex items-center gap-2">
              <Webhook className="h-4 w-4" />
              Make.com Webhook URL
            </Label>
            <Input
              id="webhook_url"
              value={formData.webhook_url}
              onChange={(e) => setFormData(prev => ({ ...prev, webhook_url: e.target.value }))}
              placeholder="https://hook.eu2.make.com/..."
              required
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="scheduled_time">Posting Time</Label>
            <Input
              id="scheduled_time"
              type="time"
              value={formData.scheduled_time}
              onChange={(e) => setFormData(prev => ({ ...prev, scheduled_time: e.target.value }))}
              required
            />
          </div>

          <div className="space-y-3">
            <Label>Days of the Week</Label>
            <div className="grid grid-cols-2 gap-3">
              {daysOfWeek.map((day) => (
                <div key={day.value} className="flex items-center space-x-2">
                  <Checkbox
                    id={day.value}
                    checked={formData.days_of_week.includes(day.value)}
                    onCheckedChange={(checked) => handleDayToggle(day.value, checked as boolean)}
                  />
                  <Label htmlFor={day.value} className="text-sm font-normal">
                    {day.label}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="sentiment">Content Tone</Label>
            <Select
              value={formData.sentiment}
              onValueChange={(value) => setFormData(prev => ({ ...prev, sentiment: value }))}
            >
              <SelectTrigger>
                <SelectValue placeholder="Select content tone" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="professional">Professional</SelectItem>
                <SelectItem value="inspirational">Inspirational</SelectItem>
                <SelectItem value="educational">Educational</SelectItem>
                <SelectItem value="thoughtful">Thoughtful</SelectItem>
                <SelectItem value="optimistic">Optimistic</SelectItem>
                <SelectItem value="funny">Funny</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="prompt">Content Prompt</Label>
            <Textarea
              id="prompt"
              placeholder="Describe what kind of content you want to generate automatically..."
              className="min-h-[100px]"
              value={formData.prompt}
              onChange={(e) => setFormData(prev => ({ ...prev, prompt: e.target.value }))}
              required
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              id="is_active"
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
            <Label htmlFor="is_active">Active</Label>
          </div>

          <Button type="submit" disabled={loading} className="w-full">
            {loading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Scheduling...
              </>
            ) : (
              "Schedule Automated Posts"
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
