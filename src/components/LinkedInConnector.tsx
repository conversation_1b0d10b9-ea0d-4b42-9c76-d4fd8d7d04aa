
import { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Label } from "@/components/ui/label";
import { Loader2, Linkedin, Check, AlertTriangle, Building2 } from "lucide-react";
import { useLinkedInOAuth, LinkedInOrganization } from "@/hooks/useLinkedInOAuth";

interface LinkedInConnectorProps {
  onPostingEntityChange?: (entity: { type: 'user' | 'organization', id: string, name: string }) => void;
}

export function LinkedInConnector({ onPostingEntityChange }: LinkedInConnectorProps) {
  const { 
    isLoading, 
    linkedInProfile, 
    startLinkedInAuth, 
    handleLinkedInCallback, 
    loadLinkedInProfile, 
    disconnectLinkedIn 
  } = useLinkedInOAuth();
  
  const [selectedEntity, setSelectedEntity] = useState<string>("");

  // Load profile on mount
  useEffect(() => {
    loadLinkedInProfile();
  }, [loadLinkedInProfile]);

  // Handle OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state) {
      console.log("Found LinkedIn OAuth callback parameters, processing...");
      handleLinkedInCallback(code, state).then((success) => {
        if (success) {
          // Clean up URL
          const newUrl = window.location.pathname;
          window.history.replaceState({}, document.title, newUrl);
          // Reload profile after successful auth
          setTimeout(() => loadLinkedInProfile(), 1000);
        }
      });
    }
  }, [handleLinkedInCallback, loadLinkedInProfile]);

  const handleEntityChange = (value: string) => {
    setSelectedEntity(value);
    
    if (onPostingEntityChange) {
      if (value === 'user' && linkedInProfile) {
        onPostingEntityChange({
          type: 'user',
          id: linkedInProfile.id,
          name: linkedInProfile.name
        });
      } else if (value.startsWith('org_') && linkedInProfile) {
        const orgId = value.replace('org_', '');
        const org = linkedInProfile.organizations.find(o => o.organizationId === orgId);
        if (org) {
          onPostingEntityChange({
            type: 'organization',
            id: org.organizationId,
            name: org.name
          });
        }
      }
    }
  };

  if (!linkedInProfile) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5 text-blue-600" />
            LinkedIn Connection
          </CardTitle>
          <CardDescription>
            Connect your LinkedIn account to enable posting
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2 text-yellow-600">
            <AlertTriangle className="h-5 w-5" />
            <span className="font-medium">LinkedIn is not connected</span>
          </div>
          <p className="text-sm text-gray-500">
            Connect your LinkedIn account to post content automatically. We'll also retrieve any organizations you manage.
          </p>
          <Button 
            onClick={startLinkedInAuth}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Connecting...
              </>
            ) : (
              <>
                <Linkedin className="mr-2 h-4 w-4" /> Connect LinkedIn
              </>
            )}
          </Button>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Linkedin className="h-5 w-5 text-blue-600" />
          LinkedIn Connected
        </CardTitle>
        <CardDescription>
          Choose who to post as
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center space-x-2 text-green-600">
          <Check className="h-5 w-5" />
          <span className="font-medium">Your LinkedIn account is connected</span>
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="posting-entity">Post as</Label>
          <Select value={selectedEntity} onValueChange={handleEntityChange}>
            <SelectTrigger>
              <SelectValue placeholder="Select who to post as" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="user">
                <div className="flex items-center gap-2">
                  <Linkedin className="h-4 w-4" />
                  {linkedInProfile.name} (Personal)
                </div>
              </SelectItem>
              {linkedInProfile.organizations.map((org) => (
                <SelectItem key={org.organizationId} value={`org_${org.organizationId}`}>
                  <div className="flex items-center gap-2">
                    <Building2 className="h-4 w-4" />
                    {org.name}
                  </div>
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        
        {linkedInProfile.organizations.length > 0 && (
          <div className="text-sm text-gray-500">
            Found {linkedInProfile.organizations.length} organization(s) you can post to
          </div>
        )}
        
        <Button 
          variant="outline" 
          onClick={disconnectLinkedIn}
          disabled={isLoading}
          className="w-full"
        >
          {isLoading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Disconnecting...
            </>
          ) : (
            "Disconnect LinkedIn"
          )}
        </Button>
      </CardContent>
    </Card>
  );
}
