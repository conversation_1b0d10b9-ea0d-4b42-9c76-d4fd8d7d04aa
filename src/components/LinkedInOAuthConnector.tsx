
import { useEffect, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Loader2, <PERSON>ed<PERSON>, Check, AlertTriangle } from "lucide-react";
import { useLinkedInOAuthFlow, LinkedInProfile } from "@/hooks/useLinkedInOAuthFlow";
import { useAuth } from "@/context/AuthContext";
import { useToast } from "@/components/ui/use-toast";

interface LinkedInOAuthConnectorProps {
  onConnectionChange?: (profile: LinkedInProfile | null) => void;
}

export function LinkedInOAuthConnector({ onConnectionChange }: LinkedInOAuthConnectorProps) {
  const { user } = useAuth();
  const { isLoading, startLinkedInAuth, handleLinkedInCallback } = useLinkedInOAuthFlow();
  const [linkedInProfile, setLinkedInProfile] = useState<LinkedInProfile | null>(null);
  const [configError, setConfigError] = useState<string | null>(null);
  const { toast } = useToast();

  // Check if LinkedIn is properly configured
  useEffect(() => {
    const checkLinkedInConfig = async () => {
      try {
        const { data, error } = await fetch('/functions/v1/get-linkedin-config', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
          },
        }).then(res => res.json());

        if (error || !data?.clientId) {
          setConfigError('LinkedIn integration is not properly configured.');
          console.error('LinkedIn configuration error:', error);
        } else {
          setConfigError(null);
        }
      } catch (error) {
        setConfigError('Unable to verify LinkedIn configuration.');
        console.error('LinkedIn config check failed:', error);
      }
    };

    checkLinkedInConfig();
  }, []);

  // Handle OAuth callback when component mounts
  useEffect(() => {
    const processCallback = async () => {
      const urlParams = new URLSearchParams(window.location.search);
      const code = urlParams.get('code');
      const state = urlParams.get('state');
      const error = urlParams.get('error');
      const errorDescription = urlParams.get('error_description');

      // Handle OAuth errors
      if (error) {
        console.error('LinkedIn OAuth error:', error, errorDescription);
        toast({
          title: "LinkedIn Connection Failed",
          description: errorDescription || "Authentication was cancelled or failed.",
          variant: "destructive"
        });
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
        return;
      }

      // Process successful callback
      if (code && state) {
        console.log('Found LinkedIn OAuth callback parameters');
        try {
          const profile = await handleLinkedInCallback(code, state);
          if (profile) {
            setLinkedInProfile(profile);
            onConnectionChange?.(profile);
            toast({
              title: "LinkedIn Connected",
              description: `Successfully connected as ${profile.name}`,
            });
          }
        } catch (error) {
          console.error('LinkedIn callback error:', error);
          toast({
            title: "LinkedIn Connection Failed",
            description: "Failed to complete LinkedIn authentication.",
            variant: "destructive"
          });
        }
        
        // Clean up URL
        window.history.replaceState({}, document.title, window.location.pathname);
      }
    };

    processCallback();
  }, [handleLinkedInCallback, onConnectionChange, toast]);

  const handleConnect = () => {
    if (configError) {
      toast({
        title: "Configuration Error",
        description: configError,
        variant: "destructive"
      });
      return;
    }
    startLinkedInAuth();
  };

  const handleDisconnect = async () => {
    // TODO: Implement disconnect functionality
    setLinkedInProfile(null);
    onConnectionChange?.(null);
    toast({
      title: "LinkedIn Disconnected",
      description: "Your LinkedIn account has been disconnected.",
    });
  };

  if (!user) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Linkedin className="h-5 w-5 text-blue-600" />
            LinkedIn Connection
          </CardTitle>
          <CardDescription>
            Please log in to connect your LinkedIn account
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Linkedin className="h-5 w-5 text-blue-600" />
          LinkedIn Connection
        </CardTitle>
        <CardDescription>
          {linkedInProfile || user.linkedin_connected 
            ? "Your LinkedIn account is connected" 
            : "Connect your LinkedIn account to enable posting"}
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {configError && (
          <div className="flex items-center space-x-2 text-red-600 bg-red-50 p-3 rounded-md">
            <AlertTriangle className="h-5 w-5" />
            <span className="text-sm">{configError}</span>
          </div>
        )}

        {linkedInProfile || user.linkedin_connected ? (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-green-600">
              <Check className="h-5 w-5" />
              <span className="font-medium">
                LinkedIn connected
                {linkedInProfile && ` as ${linkedInProfile.name}`}
              </span>
            </div>
            
            {linkedInProfile?.organizations && linkedInProfile.organizations.length > 0 && (
              <div className="space-y-2">
                <p className="text-sm font-medium">Managed Organizations:</p>
                <ul className="text-sm text-gray-600 space-y-1">
                  {linkedInProfile.organizations.map((org) => (
                    <li key={org.id} className="flex items-center space-x-2">
                      <span>• {org.name}</span>
                      {org.vanityName && <span className="text-gray-400">({org.vanityName})</span>}
                    </li>
                  ))}
                </ul>
              </div>
            )}
            
            <Button 
              variant="outline" 
              onClick={handleDisconnect}
              disabled={isLoading}
              className="w-full"
            >
              Disconnect LinkedIn
            </Button>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center space-x-2 text-yellow-600">
              <AlertTriangle className="h-5 w-5" />
              <span className="font-medium">LinkedIn is not connected</span>
            </div>
            <p className="text-sm text-gray-500">
              Connect your LinkedIn account to enable automatic posting and access to your managed organizations.
            </p>
            <Button 
              onClick={handleConnect}
              disabled={isLoading || !!configError}
              className="w-full bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Connecting...
                </>
              ) : (
                <>
                  <Linkedin className="mr-2 h-4 w-4" /> Connect LinkedIn
                </>
              )}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
