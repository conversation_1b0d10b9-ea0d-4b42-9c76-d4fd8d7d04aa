import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardDescription, Card<PERSON>oot<PERSON>, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { LinkedInConnector } from "@/components/LinkedInConnector";
import { ScheduleWebhookForm } from "@/components/ScheduleWebhookForm";
import { ScheduledPostsList } from "@/components/ScheduledPostsList";
import { useAuth } from "@/context/AuthContext";
import { Loader2, <PERSON><PERSON><PERSON>, Send, Calendar, Settings } from "lucide-react";
import { useSubscription } from "@/hooks/useSubscription";
import { useMakeWorkflow, MakeWorkflowParams } from "@/hooks/useMakeWorkflow";
import { useToast } from "@/hooks/use-toast";
import { supabase } from "@/integrations/supabase/client";
import { useAIAgentPreferences } from "@/hooks/useAIAgentPreferences";
import { useLinkedInOAuth } from "@/hooks/useLinkedInOAuth";

// The webhook URL should come from environment variables or user input in a real app
const MAKE_WEBHOOK_URL = "https://hook.eu2.make.com/0tan058b2ctrnle06mgiyo2trm2dd1nb";

export function LinkedInThoughtLeader() {
  const { user } = useAuth();
  const { hasActiveSubscription, isLoading: subscriptionLoading, refreshSubscriptions } = useSubscription();
  const { triggerWorkflow, isLoading: isGenerating } = useMakeWorkflow();
  const { toast } = useToast();
  const [isSubscribing, setIsSubscribing] = useState(false);
  const [postingEntity, setPostingEntity] = useState<{ type: 'user' | 'organization', id: string, name: string } | null>(null);
  const [formData, setFormData] = useState({
    topic: "",
    prompt: "",
    sentiment: "professional",
  });

  // LinkedIn OAuth hook
  const { handleLinkedInCallback } = useLinkedInOAuth();

  // Use the AI Agent preferences hook for persistent settings
  const { 
    preferences: agentSettings, 
    updatePreferences: updateAgentSettings, 
    savePreferences: saveAgentSettings,
    loading: settingsLoading 
  } = useAIAgentPreferences("linkedin-thought-leader");

  const isSubscribed = hasActiveSubscription("linkedin-thought-leader");

  // Handle LinkedIn OAuth callback
  useEffect(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const code = urlParams.get('code');
    const state = urlParams.get('state');

    if (code && state) {
      console.log("Found LinkedIn OAuth callback parameters");
      handleLinkedInCallback(code, state).then((success) => {
        if (success) {
          // Clean up URL parameters
          window.history.replaceState({}, document.title, '/thought-leader');
        }
      });
    }
  }, [handleLinkedInCallback]);

  // Refresh subscription status on mount
  useEffect(() => {
    if (user) {
      refreshSubscriptions();
    }
  }, [user, refreshSubscriptions]);

  const handleSubscribe = async () => {
    if (!user) {
      toast({
        title: "Error",
        description: "You must be logged in to subscribe",
        variant: "destructive",
      });
      return;
    }

    setIsSubscribing(true);
    try {
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        toast({
          title: "Authentication Error",
          description: "Your session has expired. Please log in again.",
          variant: "destructive",
        });
        return;
      }

      // Get the current origin for success/cancel URLs
      const origin = window.location.origin;
      
      // Call the Stripe checkout edge function
      const { data, error } = await supabase.functions.invoke('create-checkout', {
        body: {
          productId: "linkedin-thought-leader",
          origin,
        },
        headers: {
          Authorization: `Bearer ${session.access_token}`,
        },
      });

      if (error) throw error;

      // Redirect to Stripe checkout
      window.location.href = data.url;
    } catch (error) {
      console.error("Subscription error:", error);
      toast({
        title: "Subscription Error",
        description: "There was a problem processing your subscription. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsSubscribing(false);
    }
  };

  const handleFormChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSettingsChange = (field: string, value: string | boolean) => {
    updateAgentSettings({ [field]: value });
  };

  const handleSaveSettings = async () => {
    try {
      await saveAgentSettings();
      toast({
        title: "Settings Saved",
        description: "Your agent settings have been saved successfully.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to save settings. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleGenerateContent = async () => {
    if (!isSubscribed) {
      toast({
        title: "Subscription Required",
        description: "Please subscribe to the LinkedIn Thought Leader service first",
      });
      return;
    }

    if (!postingEntity) {
      toast({
        title: "LinkedIn Connection Required",
        description: "Please connect your LinkedIn account and select who to post as",
        variant: "destructive",
      });
      return;
    }

    if (!formData.topic || !formData.prompt) {
      toast({
        title: "Missing Information",
        description: "Please fill in all required fields",
        variant: "destructive",
      });
      return;
    }

    const params: MakeWorkflowParams = {
      topic: formData.topic,
      sentiment: formData.sentiment,
      prompt: formData.prompt,
      userId: user?.id || "",
      linkedInEntityType: postingEntity.type,
      linkedInEntityId: postingEntity.id,
      linkedInEntityName: postingEntity.name,
    };

    await triggerWorkflow(MAKE_WEBHOOK_URL, params);
  };

  return (
    <div className="max-w-4xl mx-auto px-4">
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Linkedin className="h-6 w-6 text-linkedin-blue" />
            LinkedIn Thought Leader AI Agent
          </CardTitle>
          <CardDescription>
            Establish yourself as a thought leader on LinkedIn with AI-generated content tailored to your preferences
          </CardDescription>
        </CardHeader>
        <CardContent>
          {subscriptionLoading ? (
            <div className="flex items-center justify-center p-4">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Checking subscription status...</span>
            </div>
          ) : isSubscribed ? (
            <div className="p-3 bg-green-50 border border-green-200 rounded-md text-green-700 mb-4">
              ✓ You are subscribed to the LinkedIn Thought Leader service
            </div>
          ) : (
            <div className="space-y-4">
              <p className="text-gray-600">
                Subscribe to the LinkedIn Thought Leader AI Agent to automatically generate professional content for your LinkedIn profile.
              </p>
              <ul className="space-y-2 list-disc pl-5">
                <li>Daily posts to your LinkedIn profile</li>
                <li>Topic selection based on your industry</li>
                <li>Professional tone customization</li>
                <li>Article sharing capability</li>
                <li>Performance analytics</li>
              </ul>
              <div className="font-bold text-xl mt-4">$4.99/month</div>
              <p className="text-sm text-gray-500">7-day free trial included</p>
            </div>
          )}
        </CardContent>
        <CardFooter className="flex-col items-start gap-4">
          {!isSubscribed && !subscriptionLoading && (
            <Button 
              onClick={handleSubscribe} 
              disabled={isSubscribing} 
              className="w-full bg-linkedin-blue hover:bg-linkedin-darkblue"
            >
              {isSubscribing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Processing...
                </>
              ) : "Start 7-Day Free Trial"}
            </Button>
          )}
        </CardFooter>
      </Card>

      {/* Only show the main interface if subscription is confirmed */}
      {isSubscribed && (
        <div className="space-y-6">
          <Tabs defaultValue="manual" className="w-full">
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="manual">Manual Post</TabsTrigger>
              <TabsTrigger value="settings">Agent Settings</TabsTrigger>
              <TabsTrigger value="schedule">Schedule Posts</TabsTrigger>
              <TabsTrigger value="manage">Manage Scheduled</TabsTrigger>
            </TabsList>
            
            <TabsContent value="manual">
              <div className="space-y-6">
                <LinkedInConnector onPostingEntityChange={setPostingEntity} />

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Send className="h-5 w-5" />
                      Generate LinkedIn Content
                    </CardTitle>
                    <CardDescription>
                      Create thought leadership content for immediate posting
                      {postingEntity && (
                        <span className="block mt-1 text-blue-600">
                          Posting as: {postingEntity.name} ({postingEntity.type})
                        </span>
                      )}
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="topic">Topic</Label>
                      <Input 
                        id="topic" 
                        placeholder="e.g., Digital Marketing Trends"
                        value={formData.topic}
                        onChange={(e) => handleFormChange('topic', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="sentiment">Tone</Label>
                      <Select 
                        value={formData.sentiment}
                        onValueChange={(value) => handleFormChange('sentiment', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select content tone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="professional">Professional</SelectItem>
                          <SelectItem value="inspirational">Inspirational</SelectItem>
                          <SelectItem value="educational">Educational</SelectItem>
                          <SelectItem value="thoughtful">Thoughtful</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="prompt">Content Prompt</Label>
                      <Textarea 
                        id="prompt" 
                        placeholder="Describe what kind of content you want to generate..."
                        className="min-h-[100px]"
                        value={formData.prompt}
                        onChange={(e) => handleFormChange('prompt', e.target.value)}
                      />
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button
                      onClick={handleGenerateContent}
                      disabled={isGenerating || !postingEntity}
                      className="w-full"
                    >
                      {isGenerating ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Generating...
                        </>
                      ) : (
                        <>
                          <Send className="mr-2 h-4 w-4" /> Generate Content
                        </>
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>

            <TabsContent value="settings">
              <div className="space-y-6">
                <LinkedInConnector onPostingEntityChange={setPostingEntity} />

                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Agent Settings
                    </CardTitle>
                    <CardDescription>
                      Configure your LinkedIn Thought Leader AI agent preferences
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div className="space-y-2">
                      <Label htmlFor="default-sentiment">Default Content Tone</Label>
                      <Select 
                        value={agentSettings.sentiment || "professional"}
                        onValueChange={(value) => handleSettingsChange('sentiment', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select default tone" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="professional">Professional</SelectItem>
                          <SelectItem value="inspirational">Inspirational</SelectItem>
                          <SelectItem value="educational">Educational</SelectItem>
                          <SelectItem value="thoughtful">Thoughtful</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="default-prompt">Default Content Prompt</Label>
                      <Textarea 
                        id="default-prompt" 
                        placeholder="Enter your default content generation prompt..."
                        className="min-h-[100px]"
                        value={agentSettings.prompt || ""}
                        onChange={(e) => handleSettingsChange('prompt', e.target.value)}
                      />
                    </div>

                    <div className="space-y-2">
                      <Label htmlFor="posting-schedule">Posting Schedule</Label>
                      <Select 
                        value={agentSettings.postingSchedule || "daily"}
                        onValueChange={(value) => handleSettingsChange('postingSchedule', value)}
                      >
                        <SelectTrigger>
                          <SelectValue placeholder="Select posting frequency" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="twice-weekly">Twice Weekly</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                          <SelectItem value="custom">Custom Schedule</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>

                    <div className="space-y-4">
                      <div className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id="share-articles"
                          checked={agentSettings.shareArticles || false}
                          onChange={(e) => handleSettingsChange('shareArticles', e.target.checked)}
                          className="rounded border-gray-300"
                        />
                        <Label htmlFor="share-articles">Enable article sharing</Label>
                      </div>

                      {agentSettings.shareArticles && (
                        <div className="space-y-2 pl-6">
                          <Label htmlFor="rss-keyword">RSS Keyword for Articles</Label>
                          <Input 
                            id="rss-keyword" 
                            placeholder="e.g., artificial intelligence, marketing trends"
                            value={agentSettings.rssKeyword || ""}
                            onChange={(e) => handleSettingsChange('rssKeyword', e.target.value)}
                          />
                          <p className="text-sm text-gray-500">
                            Enter keywords to find relevant articles to share. Multiple keywords can be separated by commas.
                          </p>
                        </div>
                      )}
                    </div>
                  </CardContent>
                  <CardFooter>
                    <Button 
                      onClick={handleSaveSettings}
                      disabled={settingsLoading}
                      className="w-full"
                    >
                      {settingsLoading ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
                        </>
                      ) : (
                        "Save Settings"
                      )}
                    </Button>
                  </CardFooter>
                </Card>
              </div>
            </TabsContent>
            
            <TabsContent value="schedule">
              <ScheduleWebhookForm />
            </TabsContent>
            
            <TabsContent value="manage">
              <ScheduledPostsList />
            </TabsContent>
          </Tabs>
        </div>
      )}
    </div>
  );
}
