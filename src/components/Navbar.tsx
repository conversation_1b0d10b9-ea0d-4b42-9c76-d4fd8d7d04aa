
import {Link, useLocation, useNavigate} from "react-router-dom"

import {cn} from "@/lib/utils"
import {useAuth} from "@/context/AuthContext"
import {LanguageSelector} from "@/components/LanguageSelector.tsx";
import {Button} from "@/components/ui/button";
import {LogOut, User, Settings} from "lucide-react";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuItem,
    DropdownMenuLabel,
    DropdownMenuSeparator,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

function Navbar() {
    const {isAuthenticated, user, signOut, loading} = useAuth()
    const {pathname} = useLocation()
    const navigate = useNavigate()

    const handleLogout = async () => {
        try {
            await signOut()
            navigate("/")
        } catch (error) {
            console.error("Logout error:", error)
        }
    }

    return (
        <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
            <div className="container flex h-14 items-center">
                <Link to="/" className="mr-6 flex items-center space-x-2">
          <span className="hidden font-bold sm:inline-block">
            MySocialPoster
          </span>
                </Link>
                <nav className="flex items-center gap-6 text-sm">
                    <Link
                        to="/about"
                        className={cn(
                            "transition-colors hover:text-foreground/80",
                            pathname === "/about" ? "text-foreground" : "text-foreground/60"
                        )}
                    >
                        About
                    </Link>
                    <Link
                        to="/ai-agents"
                        className={cn(
                            "transition-colors hover:text-foreground/80",
                            pathname === "/ai-agents" ? "text-foreground" : "text-foreground/60"
                        )}
                    >
                        AI Agents
                    </Link>
                    {/*<Link*/}
                    {/*  to="/dashboard"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/dashboard" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Dashboard*/}
                    {/*</Link>*/}
                    {/*<Link*/}
                    {/*  to="/thought-leader"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/thought-leader" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Thought Leader*/}
                    {/*</Link>*/}
                    {/*<Link*/}
                    {/*  to="/pricing"*/}
                    {/*  className={cn(*/}
                    {/*    "transition-colors hover:text-foreground/80",*/}
                    {/*    pathname === "/pricing" ? "text-foreground" : "text-foreground/60"*/}
                    {/*  )}*/}
                    {/*>*/}
                    {/*  Pricing*/}
                    {/*</Link>*/}
                </nav>

                <div className="ml-auto flex items-center space-x-2">
                    {isAuthenticated ? (
                        <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                                <Button variant="ghost" size="sm" className="flex items-center space-x-2">
                                    <User className="h-4 w-4" />
                                    <span className="hidden sm:inline-block">{user?.email}</span>
                                </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end" className="w-56">
                                <DropdownMenuLabel>My Account</DropdownMenuLabel>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem asChild>
                                    <Link to="/settings" className="flex items-center">
                                        <Settings className="h-4 w-4 mr-2" />
                                        Settings
                                    </Link>
                                </DropdownMenuItem>
                                <DropdownMenuSeparator />
                                <DropdownMenuItem
                                    onClick={handleLogout}
                                    disabled={loading}
                                    className="text-red-600 focus:text-red-600"
                                >
                                    <LogOut className="h-4 w-4 mr-2" />
                                    Logout
                                </DropdownMenuItem>
                            </DropdownMenuContent>
                        </DropdownMenu>
                    ) : (
                        <>
                            <LanguageSelector/>
                            <Link
                                to="/login"
                                className={cn(
                                    "transition-colors hover:text-foreground/80",
                                    pathname === "/login" ? "text-foreground" : "text-foreground/60"
                                )}
                            >
                                Login
                            </Link>
                            <Link
                                to="/signup"
                                className={cn(
                                    "transition-colors hover:text-foreground/80",
                                    pathname === "/signup" ? "text-foreground" : "text-foreground/60"
                                )}
                            >
                                Sign Up
                            </Link>
                        </>
                    )}
                </div>
            </div>
        </header>
    )
}

export default Navbar;
