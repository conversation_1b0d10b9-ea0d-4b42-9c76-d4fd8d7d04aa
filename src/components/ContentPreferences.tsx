
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { usePostPreferences } from "@/context/PostPreferencesContext";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { X, Plus, Save, Loader2 } from "lucide-react";

export function ContentPreferences() {
  const { preferences, updatePreferences, savePreferences, loading } = usePostPreferences();
  const [topicInput, setTopicInput] = useState("");
  const [localPrefs, setLocalPrefs] = useState({
    topics: [...preferences.topics],
    sentiment: preferences.sentiment,
    makeComWebhookUrl: preferences.makeComWebhookUrl || "",
  });

  const handleAddTopic = () => {
    if (topicInput.trim() && !localPrefs.topics.includes(topicInput.trim())) {
      setLocalPrefs({
        ...localPrefs,
        topics: [...localPrefs.topics, topicInput.trim()],
      });
      setTopicInput("");
    }
  };

  const handleRemoveTopic = (topic: string) => {
    setLocalPrefs({
      ...localPrefs,
      topics: localPrefs.topics.filter((t) => t !== topic),
    });
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      e.preventDefault();
      handleAddTopic();
    }
  };

  const handleSave = async () => {
    updatePreferences({
      topics: localPrefs.topics,
      sentiment: localPrefs.sentiment,
      makeComWebhookUrl: localPrefs.makeComWebhookUrl,
    });
    await savePreferences();
  };

  return (
    <div className="space-y-6">
      <div>
        <Label htmlFor="topics">Content Topics</Label>
        <div className="flex space-x-2 mt-1">
          <Input
            id="topics"
            value={topicInput}
            onChange={(e) => setTopicInput(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Add a topic (e.g., Marketing, Leadership)"
            className="flex-1"
          />
          <Button
            type="button"
            variant="outline"
            size="icon"
            onClick={handleAddTopic}
            disabled={!topicInput.trim()}
          >
            <Plus className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex flex-wrap gap-2 mt-3">
          {localPrefs.topics.length > 0 ? (
            localPrefs.topics.map((topic) => (
              <Badge key={topic} variant="secondary" className="flex items-center gap-1 px-3 py-1">
                {topic}
                <button
                  type="button"
                  onClick={() => handleRemoveTopic(topic)}
                  className="hover:text-destructive focus:outline-none"
                >
                  <X className="h-3 w-3" />
                </button>
              </Badge>
            ))
          ) : (
            <p className="text-sm text-gray-500 mt-1">
              No topics added yet. Add topics to make your content more relevant.
            </p>
          )}
        </div>
      </div>

      <div>
        <Label htmlFor="sentiment">Content Sentiment</Label>
        <Select
          value={localPrefs.sentiment}
          onValueChange={(value: any) => setLocalPrefs({ ...localPrefs, sentiment: value })}
        >
          <SelectTrigger id="sentiment" className="w-full">
            <SelectValue placeholder="Select tone" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="professional">Professional</SelectItem>
            <SelectItem value="optimistic">Optimistic</SelectItem>
            <SelectItem value="thoughtful">Thoughtful</SelectItem>
            <SelectItem value="educational">Educational</SelectItem>
            <SelectItem value="inspirational">Inspirational</SelectItem>
          </SelectContent>
        </Select>
        <p className="text-sm text-gray-500 mt-1">
          The tone and style of your LinkedIn posts
        </p>
      </div>

      <div>
        <Label htmlFor="makeWebhook">Make.com Webhook URL (Advanced)</Label>
        <Input
          id="makeWebhook"
          value={localPrefs.makeComWebhookUrl}
          onChange={(e) => setLocalPrefs({ ...localPrefs, makeComWebhookUrl: e.target.value })}
          placeholder="https://hook.make.com/..."
          className="w-full"
        />
        <p className="text-sm text-gray-500 mt-1">
          If you have your own Make.com workflow, enter the webhook URL here
        </p>
      </div>

      <div className="pt-4">
        <Button onClick={handleSave} disabled={loading}>
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Saving...
            </>
          ) : (
            <>
              <Save className="mr-2 h-4 w-4" /> Save Content Preferences
            </>
          )}
        </Button>
      </div>
    </div>
  );
}
