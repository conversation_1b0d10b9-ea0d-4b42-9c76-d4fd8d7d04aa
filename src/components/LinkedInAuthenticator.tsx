
import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Loader2, Linkedin } from "lucide-react";
import { useLinkedInOAuth } from "@/hooks/useLinkedInOAuth";

export function LinkedInAuthenticator() {
  const { startLinkedInAuth, isLoading } = useLinkedInOAuth();

  return (
    <Button
      onClick={startLinkedInAuth}
      disabled={isLoading}
      className="w-full bg-[#0072b1] hover:bg-[#00598e]"
    >
      {isLoading ? (
        <>
          <Loader2 className="mr-2 h-4 w-4 animate-spin" /> Connecting...
        </>
      ) : (
        <>
          <Linkedin className="mr-2 h-4 w-4" /> Connect LinkedIn
        </>
      )}
    </Button>
  );
}
