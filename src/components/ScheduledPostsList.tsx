
import { Card, CardContent, CardDescription, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { useScheduledWebhooks } from "@/hooks/useScheduledWebhooks";
import { Loader2, Clock, Trash2, Calendar } from "lucide-react";

export function ScheduledPostsList() {
  const { scheduledPosts, loading, updateScheduledPost, deleteScheduledPost } = useScheduledWebhooks();

  const handleToggleActive = async (id: string, isActive: boolean) => {
    await updateScheduledPost(id, { is_active: isActive });
  };

  const handleDelete = async (id: string) => {
    if (confirm("Are you sure you want to delete this scheduled post?")) {
      await deleteScheduledPost(id);
    }
  };

  const formatDays = (days: string[]) => {
    return days.map(day => day.charAt(0).toUpperCase() + day.slice(1, 3)).join(", ");
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Calendar className="h-5 w-5" />
          Scheduled Posts
        </CardTitle>
        <CardDescription>
          Manage your automated LinkedIn posting schedule
        </CardDescription>
      </CardHeader>
      <CardContent>
        {scheduledPosts.length === 0 ? (
          <div className="text-center py-8 text-gray-500">
            <Clock className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No scheduled posts yet.</p>
            <p className="text-sm">Create your first automated post in the Schedule tab.</p>
          </div>
        ) : (
          <div className="space-y-4">
            {scheduledPosts.map((post) => (
              <div key={post.id} className="border rounded-lg p-4 space-y-3">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <Badge variant={post.is_active ? "default" : "secondary"}>
                        {post.is_active ? "Active" : "Inactive"}
                      </Badge>
                      <Badge variant="outline">
                        {post.sentiment}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-2">
                      {post.prompt}
                    </p>
                    <div className="flex items-center gap-4 text-xs text-gray-500">
                      <span className="flex items-center gap-1">
                        <Clock className="h-3 w-3" />
                        {post.scheduled_time}
                      </span>
                      <span>
                        {formatDays(post.days_of_week)}
                      </span>
                      {post.last_triggered_at && (
                        <span>
                          Last: {new Date(post.last_triggered_at).toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Switch
                      checked={post.is_active}
                      onCheckedChange={(checked) => handleToggleActive(post.id, checked)}
                    />
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleDelete(post.id)}
                      className="text-red-600 hover:text-red-700"
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
