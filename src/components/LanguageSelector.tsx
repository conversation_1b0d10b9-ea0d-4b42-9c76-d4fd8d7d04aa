
import { usePostPreferences } from "@/context/PostPreferencesContext";
import { Button } from "@/components/ui/button";
import { Language } from "@/context/PostPreferencesContext";
import { Globe } from "lucide-react";
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

const languages = [
  { code: "en", name: "English", flag: "🇺🇸" },
  { code: "de", name: "<PERSON><PERSON><PERSON>", flag: "🇩🇪" },
  { code: "sk", name: "Slovenčina", flag: "🇸🇰" },
  { code: "cs", name: "<PERSON><PERSON><PERSON><PERSON>", flag: "🇨🇿" },
  { code: "es", name: "<PERSON>spa<PERSON><PERSON>", flag: "🇪🇸" },
  { code: "ru", name: "Русский", flag: "🇷🇺" }
];

export function LanguageSelector() {
  const { preferences, updatePreferences } = usePostPreferences();
  
  const currentLanguage = languages.find(lang => lang.code === preferences.language) || languages[0];
  
  const handleLanguageSelect = (langCode: Language) => {
    updatePreferences({ language: langCode });
    // Reload the page to apply translations
    window.location.reload();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="rounded-full w-8 h-8 p-0 bg-white shadow-sm border-gray-200">
          <span>{currentLanguage.flag}</span>
          <span className="sr-only">Change language</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="min-w-[8rem]">
        {languages.map((language) => (
          <DropdownMenuItem
            key={language.code}
            onClick={() => handleLanguageSelect(language.code as Language)}
            className="cursor-pointer"
          >
            <span className="mr-2">{language.flag}</span>
            {language.name}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
