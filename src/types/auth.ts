
import { Session, User } from '@supabase/supabase-js';
import { Tables } from '@/integrations/supabase/types';

export interface Subscription {
  plan: string;
  status: string;
}

// Extended user type with subscription
export interface UserWithSubscription extends Tables<'profiles'> {
  subscription?: Subscription | null;
}

// Define the possible return types for the signUp function
export interface SignUpResult {
  user?: User | null;
  session?: Session | null;
  needsVerification?: boolean;
  email?: string;
  success?: boolean;
  error?: string;
}

export interface AuthContextType {
  user: UserWithSubscription | null;
  session: Session | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  signIn: (email: string, password: string) => Promise<{ user: User; session: Session; }>;
  signUp: (email: string, password: string, name: string) => Promise<SignUpResult>;
  signOut: () => Promise<void>;
  refreshSession: () => Promise<void>;
  linkedin_connected: boolean;
  updateLinkedInConnected: (value: boolean) => Promise<void>;
  connectLinkedIn: () => Promise<void>;
  signInWithGoogle: () => Promise<void>;
  signInWithApple: () => Promise<void>;
  signInWithLinkedIn: () => Promise<void>;
  loading: boolean;
}
