
import { createContext, useContext, useState, useEffect, ReactNode, useCallback, useMemo } from 'react';
import { supabase } from '@/integrations/supabase/client';
import { AuthContextType } from '@/types/auth';
import { useAuthSessionManager } from '@/hooks/auth/useAuthSessionManager';
import { useUserProfileManager } from '@/hooks/auth/useUserProfileManager';
import { useAuthMethods } from '@/hooks/auth/useAuthMethods';
import { useWelcomeEmail } from '@/hooks/auth/useWelcomeEmail';
import { toast } from '@/hooks/use-toast';

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const {
    session,
    setSession,
    isLoading,
    setIsLoading,
    refreshSession
  } = useAuthSessionManager();

  const {
    user,
    setUser,
    linkedin_connected,
    setLinkedInConnected,
    fetchUserProfile,
    updateLinkedInConnected
  } = useUserProfileManager();

  const {
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithApple,
    signInWithLinkedIn
  } = useAuthMethods(setIsLoading);

  const { sendWelcomeEmail } = useWelcomeEmail();

  const [isInitialized, setIsInitialized] = useState(false);

  const connectLinkedIn = useCallback(async () => {
    setIsLoading(true);
    try {
      await signInWithLinkedIn();
      // OAuth redirects happen automatically
    } catch (error: any) {
      console.error("LinkedIn connection error:", error);
      toast({
        title: "Connection Failed",
        description: "Could not connect to LinkedIn. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [signInWithLinkedIn, setIsLoading]);

  // Check for authentication errors after redirect
  const checkAuthErrors = useCallback(() => {
    const urlParams = new URLSearchParams(window.location.search);
    const errorDescription = urlParams.get('error_description');
    const errorCode = urlParams.get('error');
    
    if (errorDescription || errorCode) {
      let displayMessage = errorDescription || "Authentication failed";
      
      if (errorDescription?.includes('email already in use') || 
          errorDescription?.includes('already registered') ||
          errorCode === 'user_exists') {
        displayMessage = "This email is already associated with another account. Please sign in using the original method.";
      }
      
      toast({
        title: "Authentication Error",
        description: displayMessage,
        variant: "destructive"
      });
      
      // Clean the URL
      window.history.replaceState({}, document.title, window.location.pathname);
    }
  }, []);

  useEffect(() => {
    let mounted = true;
    
    console.log("Initializing auth context...");
    
    // Set up the auth state change listener first
    const { data: { subscription } } = supabase.auth.onAuthStateChange((event, session) => {
      console.log("Auth state changed:", event, session);
      if (!mounted) return;
      
      setSession(session);
      
      if (session?.user) {
        // Use setTimeout to prevent deadlock with Supabase auth
        setTimeout(() => {
          if (mounted) {
            fetchUserProfile(session.user.id);
            
            // Send welcome email for new sign-ups via OAuth
            if (event === 'SIGNED_IN' && (
              session.user.app_metadata.provider === 'google' ||
              session.user.app_metadata.provider === 'linkedin_oidc'
            )) {
              // Check if this might be a new user
              const createdAt = new Date(session.user.created_at);
              const lastSignIn = new Date(session.user.last_sign_in_at);
              const differenceInSeconds = Math.abs((lastSignIn.getTime() - createdAt.getTime()) / 1000);
              
              // If created within 30 seconds of sign in, consider it a new user
              const isNewUser = differenceInSeconds < 30;
              
              if (isNewUser) {
                console.log("New OAuth user detected, sending welcome email");
                const userName = session.user.user_metadata.full_name || 
                                session.user.user_metadata.name || 
                                session.user.email?.split('@')[0] || 
                                'User';
                
                sendWelcomeEmail(session.user.id, session.user.email || '', userName);
              }
            }
          }
        }, 0);
      } else {
        // Clear user data when session is null
        setUser(null);
        setLinkedInConnected(false);
      }
    });

    // Check for auth errors on initial load
    checkAuthErrors();

    // Then check for existing session
    const loadSession = async () => {
      if (!mounted) return;

      setIsLoading(true);
      try {
        console.log("Loading initial session...");
        const { data: { session }, error } = await supabase.auth.getSession();

        if (error) {
          console.error("Session fetch error:", error);
          // Clear any invalid session data
          setSession(null);
          setUser(null);
          setLinkedInConnected(false);
          return;
        }

        if (mounted) {
          console.log("Initial session loaded:", session ? "authenticated" : "not authenticated");

          // Validate session is not expired
          if (session && session.expires_at) {
            const expiresAt = new Date(session.expires_at * 1000);
            const now = new Date();

            if (expiresAt <= now) {
              console.log("Session expired, attempting refresh...");
              const refreshedSession = await refreshSession();
              if (refreshedSession) {
                setSession(refreshedSession);
                if (refreshedSession.user) {
                  await fetchUserProfile(refreshedSession.user.id);
                }
              } else {
                console.log("Session refresh failed, clearing session");
                setSession(null);
                setUser(null);
                setLinkedInConnected(false);
              }
            } else {
              setSession(session);
              if (session.user) {
                await fetchUserProfile(session.user.id);
              }
            }
          } else {
            setSession(session);
            if (session?.user) {
              await fetchUserProfile(session.user.id);
            }
          }
        }
      } catch (error) {
        console.error("Error fetching session:", error);
        if (mounted) {
          setSession(null);
          setUser(null);
          setLinkedInConnected(false);
        }
      } finally {
        if (mounted) {
          setIsLoading(false);
          setIsInitialized(true);
          console.log("Auth context initialized");
        }
      }
    };

    loadSession();

    // Cleanup the subscription when component unmounts
    return () => {
      console.log("Cleaning up auth context...");
      mounted = false;
      subscription.unsubscribe();
    };
  }, [fetchUserProfile, setSession, setUser, setIsLoading, sendWelcomeEmail, checkAuthErrors]);

  // Custom sign out that clears user state
  const handleSignOut = useCallback(async () => {
    setIsLoading(true);
    try {
      await signOut();
      setUser(null);
      setSession(null);
      setLinkedInConnected(false);
      console.log("User signed out successfully");
    } catch (error) {
      console.error("Sign out error:", error);
      toast({
        title: "Sign Out Error",
        description: "There was an issue signing out. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [signOut, setUser, setSession, setLinkedInConnected, setIsLoading]);

  // Custom refresh session that fetches user profile
  const handleRefreshSession = useCallback(async () => {
    try {
      const session = await refreshSession();
      if (session?.user) {
        await fetchUserProfile(session.user.id);
      }
    } catch (error: any) {
      console.error("Session refresh error:", error);
    }
  }, [refreshSession, fetchUserProfile]);

  // Memoize context value to prevent unnecessary re-renders
  const contextValue = useMemo(() => ({
    user,
    session,
    isLoading,
    isAuthenticated: !!session,
    signIn,
    signUp,
    signOut: handleSignOut,
    refreshSession: handleRefreshSession,
    linkedin_connected,
    updateLinkedInConnected,
    connectLinkedIn,
    signInWithGoogle,
    signInWithApple,
    signInWithLinkedIn,
    loading: isLoading,
  }), [
    user, 
    session, 
    isLoading, 
    signIn, 
    signUp, 
    handleSignOut, 
    handleRefreshSession, 
    linkedin_connected, 
    updateLinkedInConnected, 
    connectLinkedIn, 
    signInWithGoogle, 
    signInWithApple,
    signInWithLinkedIn
  ]);

  // Don't render children until auth is initialized
  if (!isInitialized) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
